-- Event Photo Sharing System Database Schema (INTEGRATED APPROACH)
-- Version: 2.0 - Integrated with existing image system
-- Date: 2025-01-29
--
-- IMPORTANT: This system integrates with your existing image upload system.
-- Event photos are stored in the existing 'images' table with entity_type = 'event_photo'
-- This file only creates the additional metadata table needed for event-specific features.
--
-- DO NOT CREATE SEPARATE event_photos TABLE - Use existing images table instead!

-- Event Photo Metadata Table
-- This extends the existing images table to add event photo specific metadata
CREATE TABLE IF NOT EXISTS event_photo_metadata (
    id INT PRIMARY KEY AUTO_INCREMENT,
    image_id INT NOT NULL,
    category ENUM('vehicle', 'atmosphere', 'awards', 'vendors', 'people') NOT NULL DEFAULT 'atmosphere',
    caption TEXT,
    privacy_level ENUM('public', 'attendees', 'friends', 'private') DEFAULT 'public',
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    event_type ENUM('event', 'show') NOT NULL,
    event_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (image_id) REFERENCES images(id) ON DELETE CASCADE,
    INDEX idx_event_photo_image (image_id),
    INDEX idx_event_photo_event (event_type, event_id),
    INDEX idx_event_photo_category (category),
    INDEX idx_event_photo_privacy (privacy_level),
    INDEX idx_event_photo_location (latitude, longitude)
);

-- INTEGRATION NOTES:
--
-- Event photos are now stored in your existing 'images' table with:
-- - entity_type = 'event_photo'
-- - entity_id = 'event_123' or 'show_456' (event type + underscore + event ID)
-- - All existing image features work: thumbnails, optimization, management, etc.
--
-- The event_photo_metadata table above stores additional event-specific data:
-- - Photo category (vehicle, atmosphere, awards, vendors, people)
-- - Caption and privacy level
-- - GPS coordinates where photo was taken
-- - Reference to which event/show the photo belongs to
--
-- USAGE:
-- 1. Run this SQL file to create the event_photo_metadata table
-- 2. Event photos will automatically use your existing image upload system
-- 3. Access event photo galleries via: /image_editor/eventGallery/show/123 or /image_editor/eventGallery/event/456
--
-- FEATURES AVAILABLE:
-- ✅ Location-based photo sharing (GPS verification)
-- ✅ Photo categories and captions
-- ✅ Privacy controls
-- ✅ Integration with existing image editor
-- ✅ PWA camera support
-- ✅ Event-specific photo galleries
--
-- FUTURE ENHANCEMENTS (Optional):
-- If you want to add social features like likes/comments later, you can create additional tables
-- that reference the image_id from your existing images table.
