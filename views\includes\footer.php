    </main>
    
    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5><?php echo APP_NAME; ?></h5>
                    <p>A comprehensive application for managing car shows, judging, registrations, and vehicle displays.</p>
                </div>
                <div class="col-md-4">
                    <h5>Quick Links</h5>
                    <ul class="list-unstyled">
                        <li><a href="<?php echo BASE_URL; ?>" class="text-white">Home</a></li>
                        <li><a href="<?php echo BASE_URL; ?>/show" class="text-white">Shows</a></li>
                        <li><a href="<?php echo BASE_URL; ?>/home/<USER>" class="text-white">About</a></li>
                        <li><a href="<?php echo BASE_URL; ?>/home/<USER>" class="text-white">Contact</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Connect With Us</h5>
                    <div class="social-icons">
                        <?php 
                        // Get social media links from settings
                        $facebook_url = $settingsModel->getSetting('facebook_url', '#');
                        $twitter_url = $settingsModel->getSetting('twitter_url', '#');
                        $instagram_url = $settingsModel->getSetting('instagram_url', '#');
                        $linkedin_url = $settingsModel->getSetting('linkedin_url', '#');
                        ?>
                        <a href="<?php echo $facebook_url; ?>" class="text-white me-2" target="_blank"><i class="fab fa-facebook-f"></i></a>
                        <a href="<?php echo $twitter_url; ?>" class="text-white me-2" target="_blank"><i class="fab fa-twitter"></i></a>
                        <a href="<?php echo $instagram_url; ?>" class="text-white me-2" target="_blank"><i class="fab fa-instagram"></i></a>
                        <a href="<?php echo $linkedin_url; ?>" class="text-white me-2" target="_blank"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <?php 
                // Get footer text from settings
                require_once APPROOT . '/models/SettingsModel.php';
                $settingsModel = new SettingsModel();
                $footer_text = $settingsModel->getSetting('footer_text', APP_NAME);
                $enable_white_labeling = $settingsModel->getSetting('enable_white_labeling', '0');
                
                if ($enable_white_labeling == '1' && !empty($footer_text)) {
                    echo '<p>&copy; ' . gmdate('Y') . ' ' . $footer_text . '. All rights reserved.</p>';
                } else {
                    echo '<p>&copy; ' . gmdate('Y') . ' ' . APP_NAME . '. All rights reserved.</p>';
                }
                ?>
            </div>
        </div>
    </footer>



    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>

    <!-- Bootstrap initialization check -->
    <script>
    (function() {
        if (typeof bootstrap === 'undefined') {
            console.error('Bootstrap failed to load properly. Attempting to reload...');
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js';
            script.integrity = 'sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz';
            script.crossOrigin = 'anonymous';
            document.body.appendChild(script);
        } else {
            console.log('Bootstrap loaded successfully in footer');
        }
    })();
    </script>

    <!-- MODAL FIX: Simple instant click fix -->
    <script>
    document.addEventListener('shown.bs.modal', function(e) {
        const backdrop = document.querySelector('.modal-backdrop');
        if (backdrop) backdrop.click();
    });
    </script>

    <!-- Custom JS -->
    <script src="<?php echo BASE_URL; ?>/public/js/main.js"></script>
    <script src="<?php echo BASE_URL; ?>/public/js/image-viewer.js"></script>
    <script src="<?php echo BASE_URL; ?>/public/js/notifications.js?v=<?php echo time(); ?>"></script>
    <script src="<?php echo BASE_URL; ?>/public/js/mobile-notifications-fix.js"></script>
    <script src="<?php echo BASE_URL; ?>/public/js/desktop-notifications-fix.js"></script>
    
    <!-- PWA Features - Force fresh loading -->
    <script src="<?php echo BASE_URL; ?>/public/js/pwa-features.js?v=<?php echo time(); ?>"></script>
    <script src="<?php echo BASE_URL; ?>/public/js/camera-banner.js?v=<?php echo time(); ?>"></script>
    
    <!-- PWA Initialization -->
    <script>
        // PWA Configuration
        window.PWA_CONFIG = {
            baseUrl: '<?php echo BASE_URL; ?>',
            userId: <?php echo isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 'null'; ?>,
            userRole: '<?php echo isset($_SESSION['user_role']) ? $_SESSION['user_role'] : 'guest'; ?>',
            debugMode: <?php echo DEBUG_MODE ? 'true' : 'false'; ?>,
            vapidPublicKey: null // Will be fetched from server
        };

        // Debug logging for PWA config
        if (window.PWA_CONFIG.debugMode) {
            console.log('[PWA] Configuration:', window.PWA_CONFIG);
        }
        


        // Initialize PWA features when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializePWA);
        } else {
            initializePWA();
        }
        
        function initializePWA() {
            if (typeof PWAFeatures !== 'undefined' || typeof window.PWAFeatures !== 'undefined') {
                if (!window.pwaFeatures) {
                    const PWAClass = window.PWAFeatures || PWAFeatures;
                    window.pwaFeatures = new PWAClass();
                }

                // Update usage data
                if (window.PWA_CONFIG.userId) {
                    updatePWAUsageData();
                }

                // Add mobile navigation if on mobile
                if (window.innerWidth <= 768) {
                    addMobileNavigation();
                }
                
                // Add quick actions FAB
                addQuickActionsFAB();
                
                console.log('[PWA] PWA features initialized successfully');
            } else {
                console.error('[PWA] PWA features not loaded - PWAFeatures class not found');
                console.log('[PWA] Available globals:', {
                    PWAFeatures: typeof PWAFeatures,
                    'window.PWAFeatures': typeof window.PWAFeatures,
                    'window.pwaFeatures': typeof window.pwaFeatures
                });

                // Try to load PWA features with a delay
                setTimeout(() => {
                    if (typeof PWAFeatures !== 'undefined' || typeof window.PWAFeatures !== 'undefined') {
                        console.log('[PWA] Retrying PWA initialization...');
                        initializePWA();
                    } else {
                        console.error('[PWA] PWA features still not available after retry');
                    }
                }, 1000);
            }
        }
        
        function updatePWAUsageData() {
            const usageData = {
                isInstalled: window.matchMedia('(display-mode: standalone)').matches,
                isStandalone: window.navigator.standalone === true,
                supportsPush: 'serviceWorker' in navigator && 'PushManager' in window,
                timestamp: new Date().toISOString()
            };
            
            fetch('<?php echo BASE_URL; ?>/api/pwa/usage', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify(usageData)
            }).catch(error => {
                if (window.PWA_CONFIG.debugMode) {
                    console.log('[PWA] Usage data update failed:', error);
                }
            });
        }
        

        
        function forceAppRefresh() {
            console.log('[PWA] Force refresh triggered by user');

            // Show loading indicator
            const refreshBtn = event.target.closest('.mobile-nav-item');
            const originalHTML = refreshBtn.innerHTML;
            refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>Clearing...</span>';

            // Set a maximum timeout to prevent hanging
            const maxTimeout = setTimeout(() => {
                console.log('[PWA] Refresh timeout, forcing reload...');
                window.location.reload(true);
            }, 5000); // 5 second maximum

            // Simple and fast cache clearing
            const clearCaches = async () => {
                try {
                    // Quick storage clear
                    localStorage.clear();
                    sessionStorage.clear();

                    // Quick cache clear with timeout
                    if ('caches' in window) {
                        const cacheNames = await Promise.race([
                            caches.keys(),
                            new Promise(resolve => setTimeout(() => resolve([]), 2000))
                        ]);

                        if (cacheNames.length > 0) {
                            await Promise.race([
                                Promise.all(cacheNames.map(name => caches.delete(name))),
                                new Promise(resolve => setTimeout(resolve, 2000))
                            ]);
                        }
                    }

                    // Quick service worker unregister
                    if ('serviceWorker' in navigator) {
                        const registrations = await Promise.race([
                            navigator.serviceWorker.getRegistrations(),
                            new Promise(resolve => setTimeout(() => resolve([]), 1000))
                        ]);

                        registrations.forEach(reg => reg.unregister().catch(() => {}));
                    }

                } catch (e) {
                    console.log('[PWA] Cache clear error (continuing):', e);
                }
            };

            // Execute clearing and reload
            clearCaches().then(() => {
                clearTimeout(maxTimeout);
                refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>Reloading...</span>';

                // Force reload with cache busting
                const timestamp = Date.now();
                const currentUrl = window.location.pathname + window.location.search;
                const separator = currentUrl.includes('?') ? '&' : '?';
                const newUrl = currentUrl + separator + 'refresh=' + timestamp + '&nocache=' + timestamp;

                window.location.replace(newUrl);

            }).catch(() => {
                clearTimeout(maxTimeout);
                console.log('[PWA] Cache clear failed, forcing reload...');
                window.location.reload(true);
            });
        }

        function addMobileNavigation() {
            // Create enhanced mobile bottom navigation with "More" menu
            const mobileNav = document.createElement('div');
            mobileNav.className = 'mobile-nav-bottom show';

            // Get user role for context-aware navigation
            const userRole = '<?php echo $_SESSION['user_role'] ?? 'guest'; ?>';
            const isLoggedIn = <?php echo isset($_SESSION['user_id']) ? 'true' : 'false'; ?>;

            mobileNav.innerHTML = `
                <div class="mobile-nav-items">
                    <a href="<?php echo BASE_URL; ?>/" class="mobile-nav-item ${window.location.pathname === '/' ? 'active' : ''}">
                        <i class="fas fa-home"></i>
                        <span>Home</span>
                    </a>
                    <a href="<?php echo BASE_URL; ?>/calendar" class="mobile-nav-item ${window.location.pathname.includes('/calendar') || window.location.pathname.includes('/show') ? 'active' : ''}">
                        <i class="fas fa-calendar"></i>
                        <span>Calendar</span>
                    </a>
                    ${isLoggedIn ? `
                    <a href="<?php echo BASE_URL; ?>/home/<USER>" class="mobile-nav-item center-dashboard ${window.location.pathname.includes('/dashboard') || window.location.pathname.includes('/user/') || window.location.pathname.includes('/admin/') || window.location.pathname.includes('/coordinator/') || window.location.pathname.includes('/judge/') || window.location.pathname.includes('/staff/') ? 'active' : ''}">
                        <img src="<?php echo BASE_URL; ?>/public/images/dashboard.png" alt="Dashboard - Access your personalized dashboard" class="dashboard-image">
                    </a>
                    <a href="<?php echo BASE_URL; ?>/notification_center" class="mobile-nav-item ${window.location.pathname.includes('/notification_center') ? 'active' : ''}" id="mobile-messages-link">
                        <div class="position-relative">
                            <i class="fas fa-bell" id="mobile-bell-icon"></i>
                        </div>
                        <span>MESSAGE</span>
                    </a>
                    ` : `
                    <a href="<?php echo BASE_URL; ?>/guest/hostConversion" class="mobile-nav-item center-dashboard ${window.location.pathname.includes('/guest/hostConversion') ? 'active' : ''}">
                        <img src="<?php echo BASE_URL; ?>/public/images/dashboard_guest.png" alt="GET STARTED" class="dashboard-image">
                    </a>
                    <a href="<?php echo BASE_URL; ?>/auth/login" class="mobile-nav-item ${window.location.pathname.includes('/auth/login') || window.location.pathname.includes('/progressive_auth/register') ? 'active' : ''}">
                        <i class="fas fa-sign-in-alt"></i>
                        <span>Login</span>
                    </a>
                    `}
                    <a href="#" class="mobile-nav-item" onclick="toggleMobileMoreMenu()" id="mobile-more-btn">
                        <i class="fas fa-ellipsis-h"></i>
                        <span>More</span>
                    </a>
                </div>
            `;

            // Add "More" menu overlay
            const moreMenu = document.createElement('div');
            moreMenu.className = 'mobile-more-menu';
            moreMenu.id = 'mobileMoreMenu';
            moreMenu.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 9999;
                display: none;
                align-items: flex-end;
                justify-content: center;
            `;

            // Generate role-specific more menu content
            const moreMenuContent = getMobileMoreMenuContent(userRole, isLoggedIn);
            moreMenu.innerHTML = `
                <div class="more-menu-backdrop" onclick="closeMobileMoreMenu()" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.5); backdrop-filter: blur(3px);"></div>
                <div class="more-menu-content" style="position: relative; background: white; border-radius: 20px 20px 0 0; width: 100%; max-height: 70vh; overflow-y: auto; animation: slideUpMenu 0.3s ease-out; box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.2);">
                    <div class="more-menu-header" style="display: flex; align-items: center; justify-content: space-between; padding: 20px 20px 10px; border-bottom: 1px solid #eee;">
                        <h6 style="margin: 0; font-weight: 600; color: #333; font-size: 1.1rem;">Quick Access</h6>
                        <button onclick="closeMobileMoreMenu()" style="background: none; border: none; font-size: 1.5rem; color: #666; cursor: pointer; width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">×</button>
                    </div>
                    <div class="more-menu-items" style="padding: 10px 0 20px;">
                        ${moreMenuContent}
                    </div>
                </div>
            `;

            document.body.appendChild(mobileNav);
            document.body.appendChild(moreMenu);

            // Add CSS animation for slide-up effect
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideUpMenu {
                    from {
                        transform: translateY(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateY(0);
                        opacity: 1;
                    }
                }
            `;
            document.head.appendChild(style);

            // Adjust body padding for mobile nav
            document.body.style.paddingBottom = '80px';
        }

        // Get role-specific content for mobile "More" menu
        function getMobileMoreMenuContent(userRole, isLoggedIn) {
            const baseUrl = '<?php echo BASE_URL; ?>';

            if (!isLoggedIn) {
                return `
                    <a href="${baseUrl}/auth/register" class="more-menu-item" style="display: flex; align-items: center; gap: 15px; padding: 15px 20px; color: #333; text-decoration: none; border-bottom: 1px solid #f8f9fa;">
                        <i class="fas fa-user-plus" style="width: 20px; text-align: center; font-size: 1.1rem; color: #666;"></i>
                        <span style="font-weight: 500; font-size: 1rem;">Sign Up</span>
                    </a>
                    <a href="#" class="more-menu-item" data-qr-scanner style="display: flex; align-items: center; gap: 15px; padding: 15px 20px; color: #333; text-decoration: none; border-bottom: 1px solid #f8f9fa;">
                        <i class="fas fa-qrcode" style="width: 20px; text-align: center; font-size: 1.1rem; color: #666;"></i>
                        <span style="font-weight: 500; font-size: 1rem;">Scan QR</span>
                    </a>
                    <a href="<?php echo BASE_URL; ?>/auth/login" class="more-menu-item" style="display: flex; align-items: center; gap: 15px; padding: 15px 20px; color: #333; text-decoration: none;">
                        <i class="fas fa-sign-in-alt" style="width: 20px; text-align: center; font-size: 1.1rem; color: #666;"></i>
                        <span style="font-weight: 500; font-size: 1rem;">Login</span>
                    </a>
                `;
            }

            const itemStyle = 'display: flex; align-items: center; gap: 15px; padding: 15px 20px; color: #333; text-decoration: none; border-bottom: 1px solid #f8f9fa;';
            const iconStyle = 'width: 20px; text-align: center; font-size: 1.1rem; color: #666;';
            const textStyle = 'font-weight: 500; font-size: 1rem;';

            let content = `
                <a href="${baseUrl}/user/dashboard" class="more-menu-item" style="${itemStyle}">
                    <i class="fas fa-tachometer-alt" style="${iconStyle}"></i>
                    <span style="${textStyle}">Dashboard</span>
                </a>
            `;

            // Role-specific menu items
            switch(userRole) {
                case 'admin':
                    content += `
                        <a href="${baseUrl}/admin/users" class="more-menu-item" style="${itemStyle}">
                            <i class="fas fa-users" style="${iconStyle}"></i>
                            <span style="${textStyle}">User Management</span>
                        </a>
                        <a href="${baseUrl}/admin/shows" class="more-menu-item" style="${itemStyle}">
                            <i class="fas fa-calendar-check" style="${iconStyle}"></i>
                            <span style="${textStyle}">Show Management</span>
                        </a>
                        <a href="${baseUrl}/admin/settings" class="more-menu-item" style="${itemStyle}">
                            <i class="fas fa-cog" style="${iconStyle}"></i>
                            <span style="${textStyle}">Settings</span>
                        </a>
                    `;
                    break;

                case 'coordinator':
                    content += `
                        <a href="${baseUrl}/user/createShow" class="more-menu-item" style="${itemStyle}">
                            <i class="fas fa-plus-circle" style="${iconStyle}"></i>
                            <span style="${textStyle}">Create Show</span>
                        </a>
                        <a href="${baseUrl}/admin/shows" class="more-menu-item" style="${itemStyle}">
                            <i class="fas fa-calendar-check" style="${iconStyle}"></i>
                            <span style="${textStyle}">Manage Shows</span>
                        </a>
                        <a href="${baseUrl}/calendar/createEvent" class="more-menu-item" style="${itemStyle}">
                            <i class="fas fa-calendar-plus" style="${iconStyle}"></i>
                            <span style="${textStyle}">Create Event</span>
                        </a>
                    `;
                    break;

                case 'judge':
                    content += `
                        <a href="${baseUrl}/judge/assignments" class="more-menu-item" style="${itemStyle}">
                            <i class="fas fa-tasks" style="${iconStyle}"></i>
                            <span style="${textStyle}">Assignments</span>
                        </a>
                        <a href="${baseUrl}/judge/score" class="more-menu-item" style="${itemStyle}">
                            <i class="fas fa-star" style="${iconStyle}"></i>
                            <span style="${textStyle}">Score Vehicles</span>
                        </a>
                    `;
                    break;

                case 'staff':
                    content += `
                        <a href="${baseUrl}/staff/registrations" class="more-menu-item" style="${itemStyle}">
                            <i class="fas fa-list" style="${iconStyle}"></i>
                            <span style="${textStyle}">Registrations</span>
                        </a>
                        <a href="${baseUrl}/staff/checkin" class="more-menu-item" style="${itemStyle}">
                            <i class="fas fa-check-circle" style="${iconStyle}"></i>
                            <span style="${textStyle}">Check-in</span>
                        </a>
                    `;
                    break;

                default: // user
                    content += `
                        <a href="${baseUrl}/user/vehicles" class="more-menu-item" style="${itemStyle}">
                            <i class="fas fa-car" style="${iconStyle}"></i>
                            <span style="${textStyle}">My Vehicles</span>
                        </a>
                        <a href="${baseUrl}/user/registrations" class="more-menu-item" style="${itemStyle}">
                            <i class="fas fa-list" style="${iconStyle}"></i>
                            <span style="${textStyle}">My Registrations</span>
                        </a>
                        <a href="${baseUrl}/user/profile" class="more-menu-item" style="${itemStyle}">
                            <i class="fas fa-user" style="${iconStyle}"></i>
                            <span style="${textStyle}">Profile</span>
                        </a>
                    `;
                    break;
            }

            // Common navigation items for all logged-in users
            content += `
                <a href="${baseUrl}/auth/logout" class="more-menu-item" style="${itemStyle}">
                    <i class="fas fa-sign-out-alt" style="${iconStyle}"></i>
                    <span style="${textStyle}">Logout</span>
                </a>
                <?php if (isset($_SESSION['user_id']) && $_SESSION['user_id'] == 3): ?>
                <a href="#" class="more-menu-item" onclick="forceAppRefresh()" style="${itemStyle.replace('#333', '#1338BE')}">
                    <i class="fas fa-sync-alt" style="${iconStyle.replace('#666', '#1338BE')}"></i>
                    <span style="${textStyle}">Force Refresh</span>
                </a>
                <?php endif; ?>
            `;

            return content;
        }

        // Toggle mobile "More" menu
        function toggleMobileMoreMenu() {
            const menu = document.getElementById('mobileMoreMenu');
            const moreBtn = document.getElementById('mobile-more-btn');

            if (menu.style.display === 'none') {
                menu.style.display = 'flex';
                moreBtn.classList.add('active');
                document.body.style.overflow = 'hidden';
            } else {
                closeMobileMoreMenu();
            }
        }

        // Close mobile "More" menu
        function closeMobileMoreMenu() {
            const menu = document.getElementById('mobileMoreMenu');
            const moreBtn = document.getElementById('mobile-more-btn');

            if (menu) {
                menu.style.display = 'none';
                moreBtn.classList.remove('active');
                document.body.style.overflow = '';
            }
        }

        // Make functions global so they can be called from onclick
        window.toggleMobileMoreMenu = toggleMobileMoreMenu;
        window.closeMobileMoreMenu = closeMobileMoreMenu;

        
        function addQuickActionsFAB() {
            // Don't create FAB on desktop screens (992px and above)
            if (window.innerWidth >= 992) {
                return;
            }

            <?php if (isset($_SESSION['user_id'])): ?>
            const fab = document.createElement('div');
            fab.className = 'quick-actions-fab';

            // Get current page context for smart FAB actions
            const currentPath = window.location.pathname;
            const isShowPage = currentPath.includes('/show/view/') || currentPath.includes('/show/');
            const isVehiclePage = currentPath.includes('/vehicle/') || currentPath.includes('/user/vehicles');
            const isDashboard = currentPath.includes('/dashboard');
            const isAdminPage = currentPath.includes('/admin/');
            const isCalendarPage = currentPath.includes('/calendar');

            let fabActions = '';
            let fabIcon = 'fa-plus';
            let fabColor = '#dc3545'; // Default red

            // Context-specific primary action (main FAB button)
            if (isShowPage) {
                // On show pages - primary action is registration
                <?php
                $showRegisterButton = false;
                if (isset($data['show']) && is_object($data['show'])) {
                    $now = new DateTime();
                    $registrationStart = new DateTime($data['show']->registration_start);
                    $registrationEnd = new DateTime($data['show']->registration_end);

                    if ($now >= $registrationStart && $now <= $registrationEnd && $data['show']->status === 'published') {
                        $showRegisterButton = true;
                    }
                }
                ?>

                <?php if ($showRegisterButton): ?>
                fabIcon = 'fa-car';
                fabColor = '#28a745'; // Green for registration
                fabActions += `<button class="fab-action primary-action" onclick="window.location.href='<?php echo BASE_URL; ?>/registration/register/<?php echo $data['show']->id; ?>'" title="Register Vehicle">
                    <i class="fas fa-car"></i>
                    <span>Register</span>
                </button>`;
                <?php endif; ?>

                fabActions += `<button class="fab-action" onclick="addToCalendar()" title="Add to Calendar">
                    <i class="fas fa-calendar-plus"></i>
                </button>`;

                fabActions += `<button class="fab-action" onclick="shareShow()" title="Share Show">
                    <i class="fas fa-share-alt"></i>
                </button>`;

            } else if (isVehiclePage) {
                // On vehicle pages - primary action is add/edit vehicle
                fabIcon = 'fa-car';
                fabColor = '#007bff'; // Blue for vehicles

                <?php if (isset($_SESSION['user_id'])): ?>
                fabActions += `<button class="fab-action primary-action" onclick="window.location.href='<?php echo BASE_URL; ?>/user/addVehicle'" title="Add Vehicle">
                    <i class="fas fa-plus"></i>
                    <span>Add Vehicle</span>
                </button>`;
                <?php else: ?>
                fabActions += `<button class="fab-action primary-action" onclick="window.location.href='<?php echo BASE_URL; ?>/auth/login'" title="Login to Add Vehicle">
                    <i class="fas fa-plus"></i>
                    <span>Add Vehicle</span>
                </button>`;
                <?php endif; ?>

                fabActions += `<button class="fab-action" data-camera-capture="vehicle_image" title="Take Photo">
                    <i class="fas fa-camera"></i>
                </button>`;

            } else if (isDashboard) {
                // On dashboard - primary action based on user state
                fabIcon = 'fa-tachometer-alt';
                fabColor = '#6f42c1'; // Purple for dashboard

                // Check if user has vehicles (this would need to be passed from controller)
                <?php if (isset($_SESSION['user_id'])): ?>
                fabActions += `<button class="fab-action primary-action" onclick="window.location.href='<?php echo BASE_URL; ?>/user/addVehicle'" title="Add Vehicle">
                    <i class="fas fa-car"></i>
                    <span>Add Vehicle</span>
                </button>`;
                <?php else: ?>
                fabActions += `<button class="fab-action primary-action" onclick="window.location.href='<?php echo BASE_URL; ?>/auth/login'" title="Login to Add Vehicle">
                    <i class="fas fa-car"></i>
                    <span>Add Vehicle</span>
                </button>`;
                <?php endif; ?>

                fabActions += `<button class="fab-action" onclick="window.location.href='<?php echo BASE_URL; ?>/show'" title="Find Shows">
                    <i class="fas fa-search"></i>
                </button>`;

            } else if (isAdminPage) {
                // On admin pages - admin-specific actions
                fabIcon = 'fa-crown';
                fabColor = '#dc3545'; // Red for admin

                <?php if (isset($_SESSION['user_role']) && ($_SESSION['user_role'] === 'admin' || $_SESSION['user_role'] === 'coordinator')): ?>
                fabActions += `<button class="fab-action primary-action" onclick="window.location.href='<?php echo BASE_URL; ?>/admin/addShow'" title="Create Show">
                    <i class="fas fa-plus-circle"></i>
                    <span>Create Show</span>
                </button>`;

                fabActions += `<button class="fab-action" onclick="window.location.href='<?php echo BASE_URL; ?>/admin/users'" title="Manage Users">
                    <i class="fas fa-users"></i>
                </button>`;
                <?php elseif (isset($_SESSION['user_id'])): ?>
                // Regular users can create shows too
                fabActions += `<button class="fab-action primary-action" onclick="window.location.href='<?php echo BASE_URL; ?>/user/createShow'" title="Create Show">
                    <i class="fas fa-plus-circle"></i>
                    <span>Create Show</span>
                </button>`;
                <?php else: ?>
                // Guests redirect to login
                fabActions += `<button class="fab-action primary-action" onclick="window.location.href='<?php echo BASE_URL; ?>/auth/login'" title="Login to Create Show">
                    <i class="fas fa-plus-circle"></i>
                    <span>Create Show</span>
                </button>`;
                <?php endif; ?>

            } else if (isCalendarPage) {
                // On calendar pages - event-specific actions
                fabIcon = 'fa-calendar';
                fabColor = '#fd7e14'; // Orange for calendar

                <?php if (isset($_SESSION['user_id'])): ?>
                fabActions += `<button class="fab-action primary-action" onclick="window.location.href='<?php echo BASE_URL; ?>/calendar/createEvent'" title="Create Event">
                    <i class="fas fa-calendar-plus"></i>
                    <span>Create Event</span>
                </button>`;
                <?php else: ?>
                fabActions += `<button class="fab-action primary-action" onclick="window.location.href='<?php echo BASE_URL; ?>/auth/login'" title="Login to Create Event">
                    <i class="fas fa-calendar-plus"></i>
                    <span>Create Event</span>
                </button>`;
                <?php endif; ?>

            } else {
                // Default/general pages - most common actions
                fabActions += `<button class="fab-action" data-qr-scanner title="Scan QR Code">
                    <i class="fas fa-qrcode"></i>
                </button>`;

                fabActions += `<button class="fab-action" data-camera-capture="vehicle_image" title="Take Photo">
                    <i class="fas fa-camera"></i>
                </button>`;
            }

            // Only show FAB if there are actions
            if (fabActions.trim()) {
                fab.innerHTML = `
                    <button class="fab-main" onclick="toggleQuickActions()" style="background-color: ${fabColor};" title="Quick Actions">
                        <i class="fas ${fabIcon}"></i>
                    </button>
                    <div class="fab-actions" id="fab-actions">
                        ${fabActions}
                    </div>
                `;

                document.body.appendChild(fab);
            }
            <?php endif; ?>
        }
        
        function toggleQuickActions() {
            const actions = document.getElementById('fab-actions');
            if (actions) {
                actions.classList.toggle('show');
            }
        }

        // Helper functions for FAB actions
        window.addToCalendar = function() {
            // Add current show to calendar
            if (typeof addShowToCalendar === 'function') {
                addShowToCalendar();
            } else {
                alert('Calendar feature coming soon!');
            }
        };

        window.shareShow = function() {
            // Share current show
            if (navigator.share) {
                navigator.share({
                    title: document.title,
                    url: window.location.href
                });
            } else {
                // Fallback - copy to clipboard
                navigator.clipboard.writeText(window.location.href).then(() => {
                    alert('Link copied to clipboard!');
                });
            }
        };
        
        // Handle window resize to show/hide FAB based on screen size
        window.addEventListener('resize', function() {
            const fab = document.querySelector('.quick-actions-fab');
            
            if (window.innerWidth >= 992) {
                // Desktop - hide FAB if it exists
                if (fab) {
                    fab.style.display = 'none';
                }
            } else {
                // Mobile/Tablet - show FAB if it exists, or create it if it doesn't
                if (fab) {
                    fab.style.display = 'block';
                } else {
                    // Re-create FAB for mobile if it doesn't exist
                    addQuickActionsFAB();
                }
            }
        });
        
        // Handle online/offline status
        window.addEventListener('online', function() {
            if (window.pwaFeatures) {
                window.pwaFeatures.handleOnlineStatus();
            }
        });
        
        window.addEventListener('offline', function() {
            if (window.pwaFeatures) {
                window.pwaFeatures.handleOfflineStatus();
            }
        });
        
        // Handle app installation
        window.addEventListener('beforeinstallprompt', function(e) {
            e.preventDefault();
            if (window.pwaFeatures) {
                window.pwaFeatures.deferredPrompt = e;
                window.pwaFeatures.showInstallButton();
            }
        });
        
        // Handle app installed
        window.addEventListener('appinstalled', function() {
            if (window.pwaFeatures) {
                window.pwaFeatures.showWelcomeMessage();
            }
        });
    </script>
    
    <?php 
    // Clear cache buster from session after it's been used once
    if (isset($_SESSION['cache_buster'])) {
        unset($_SESSION['cache_buster']);
    }
    ?>
    
    <!-- Dropdown Fix Script - Revised -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Skip if we're on the image_editor/browse page (it has its own handling)
        if (window.location.pathname.includes('/image_editor/browse') || 
            document.body.classList.contains('image-browse-page')) {
            console.log('Skipping global dropdown fix for image browse page');
            return;
        }
        
        // Let Bootstrap handle the dropdown toggling
        // We'll just fix the positioning after Bootstrap shows the dropdown
        
        // This approach works with Bootstrap's native dropdown implementation
        document.addEventListener('shown.bs.dropdown', function(e) {
            const toggle = e.target;
            const dropdown = toggle.nextElementSibling;
            
            if (!dropdown || !dropdown.classList.contains('dropdown-menu')) {
                return;
            }
            
            // Get toggle button position
            const toggleRect = toggle.getBoundingClientRect();
            const isMobile = window.innerWidth < 768;
            
            // Fix dropdown position
            if (isMobile) {
                // On mobile, position with minimal gap
                dropdown.style.marginTop = '2px';
            } else {
                // On desktop, position with small gap
                dropdown.style.marginTop = '1px';
            }
            
            // Ensure dropdown doesn't go off-screen horizontally
            const dropdownRect = dropdown.getBoundingClientRect();
            if (dropdownRect.right > window.innerWidth) {
                // If dropdown goes off right edge, align it to the right of the toggle
                dropdown.style.left = 'auto';
                dropdown.style.right = '0';
            }
            
            // Fix for dropdowns inside tables or other containers with overflow hidden
            if (dropdown.closest('.table-responsive') || 
                dropdown.closest('.overflow-hidden') ||
                dropdown.closest('.card-body')) {
                
                // Move dropdown to body for proper display
                const dropdownClone = dropdown.cloneNode(true);
                
                // Note: We can't reliably copy event listeners across cloned nodes
                // So we'll just make sure the dropdown works for basic clicking
                
                // Position the cloned dropdown
                dropdownClone.style.position = 'absolute';
                dropdownClone.style.top = (toggleRect.bottom + window.scrollY) + 'px';
                dropdownClone.style.left = (toggleRect.left + window.scrollX) + 'px';
                dropdownClone.style.zIndex = '999999';
                
                // Replace the original dropdown
                dropdown.style.display = 'none';
                document.body.appendChild(dropdownClone);
                
                // Clean up when dropdown is hidden
                document.addEventListener('hidden.bs.dropdown', function handler() {
                    if (document.body.contains(dropdownClone)) {
                        document.body.removeChild(dropdownClone);
                    }
                    dropdown.style.display = '';
                    document.removeEventListener('hidden.bs.dropdown', handler);
                }, { once: true });
            }
        });
    });
    </script>
</body>
</html>