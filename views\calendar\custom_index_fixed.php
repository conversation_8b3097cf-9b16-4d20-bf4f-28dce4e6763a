<?php 
/**
 * Monthly Event Chart Calendar View
 * 
 * Version: 3.68.1
 * Latest Update: 2025-01-27
 * 
 * Recent Changes (v3.68.1):
 * - ENHANCED: Added hosting clubs display to calendar event hover popups
 * - IMPROVED: Small and elegant club badges shown at bottom of popup
 * - ADDED: Hover effects and responsive design for club badges
 * - UPDATED: Database queries to include club information in event data
 * - OPTIMIZED: Automatic parsing of multiple clubs per event with proper formatting
 */
require APPROOT . '/views/includes/header.php'; 
?>
<script>
    // Define constants for JavaScript
    const URLROOT = '<?php echo URLROOT; ?>';
    const DEBUG_MODE = <?php echo defined('DEBUG_MODE') && DEBUG_MODE ? 'true' : 'false'; ?>;
    
    // Cache busting information for debugging
    if (DEBUG_MODE) {
        console.log('=== CACHE BUSTING INFO ===');
        console.log('Monthly Event Chart JS:', '<?php echo filemtime(APPROOT . '/public/js/monthly-event-chart.js'); ?>');
        console.log('Calendar Filters JS:', '<?php echo filemtime(APPROOT . '/public/js/calendar-filters.js'); ?>');

        console.log('Monthly Event Chart CSS:', '<?php echo filemtime(APPROOT . '/public/css/monthly-event-chart.css'); ?>');
        console.log('=== END CACHE BUSTING INFO ===');
    }
</script>

<!-- Timezone Helper removed - controller already converts dates -->

<!-- Monthly Event Chart CSS and JavaScript with cache-busting -->
<link rel="stylesheet" href="<?php echo URLROOT; ?>/public/css/monthly-event-chart.css?v=<?php echo filemtime(APPROOT . '/public/css/monthly-event-chart.css'); ?>">
<script src="<?php echo URLROOT; ?>/public/js/monthly-event-chart.js?v=<?php echo filemtime(APPROOT . '/public/js/monthly-event-chart.js'); ?>"></script>
<?php // Debug script removed - uses TimezoneHelper which we don't need ?>

<!-- Calendar Filters with cache-busting -->
<script src="<?php echo URLROOT; ?>/public/js/calendar-filters.js?v=<?php echo filemtime(APPROOT . '/public/js/calendar-filters.js'); ?>"></script>

<script>
    // Test if MonthlyEventChart class is loaded
    document.addEventListener('DOMContentLoaded', function() {
        if (DEBUG_MODE) {
            console.log('Testing MonthlyEventChart class availability...');
            console.log('MonthlyEventChart available:', typeof MonthlyEventChart !== 'undefined');
            if (typeof MonthlyEventChart !== 'undefined') {
                console.log('MonthlyEventChart class:', MonthlyEventChart);
            }
        }
    });
</script>

<div class="container-fluid mt-4">
    <!-- Mobile-First Responsive Navigation Bar -->
    <div class="row mb-4">
        <!-- Title Section -->
        <div class="col-12 col-lg-6 mb-3 mb-lg-0">
            <h1 class="mb-1">Monthly Calendar</h1>
            <p class="text-muted mb-0">Timeline view of events with unlimited rows</p>
        </div>
        
        <!-- Navigation Buttons Section -->
        <div class="col-12 col-lg-6">
            <div class="header-nav-buttons d-flex flex-column flex-sm-row gap-2 justify-content-lg-end">
                <!-- View Toggle Buttons -->
                <div class="btn-group flex-fill flex-sm-auto" role="group" aria-label="View toggle">
                    <a href="<?php echo URLROOT; ?>/calendar" class="btn btn-primary active d-flex align-items-center justify-content-center">
                        <i class="fas fa-chart-event me-1 me-sm-2"></i>
                        <span class="d-none d-sm-inline">Event </span>View
                    </a>
                    <a href="<?php echo URLROOT; ?>/calendar/map" class="btn btn-outline-primary d-flex align-items-center justify-content-center">
                        <i class="fas fa-map-marker-alt me-1 me-sm-2"></i>
                        <span class="d-none d-sm-inline">Map </span>View
                    </a>
                </div>
                
                <!-- Action Buttons with Dropdown -->
                <?php if (isLoggedIn()): ?>
                <div class="btn-group flex-fill flex-sm-auto" role="group">
                    <a href="<?php echo URLROOT; ?>/calendar/createEvent" class="btn btn-primary d-flex align-items-center justify-content-center">
                        <i class="fas fa-plus me-1 me-sm-2"></i>
                        <span class="d-none d-sm-inline">Add </span>Event
                    </a>
                    <button type="button" class="btn btn-primary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
                        <span class="visually-hidden">Toggle Dropdown</span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <?php if (isLoggedIn()): ?>
                        <li><a class="dropdown-item" href="<?php echo URLROOT; ?>/user/createShow">
                            <i class="fas fa-car me-2"></i>Create Show
                        </a></li>
                        <li><a class="dropdown-item" href="<?php echo URLROOT; ?>/calendar/createCalendar">
                            <i class="fas fa-plus me-2"></i>Create Calendar
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <?php endif; ?>
                        <?php if (isAdmin()): ?>
                        <li><a class="dropdown-item" href="<?php echo URLROOT; ?>/calendar/manageCalendars">
                            <i class="fas fa-calendar me-2"></i>Manage Calendars
                        </a></li>
                        <li><a class="dropdown-item" href="<?php echo URLROOT; ?>/calendar/manageVenues">
                            <i class="fas fa-map-marker-alt me-2"></i>Manage Venues
                        </a></li>
                        <li><a class="dropdown-item" href="<?php echo URLROOT; ?>/calendar/manageClubs">
                            <i class="fas fa-users me-2"></i>Manage Clubs
                        </a></li>
                        <?php endif; ?>
                        <?php if (isLoggedIn()): ?>
                        <li><a class="dropdown-item" href="<?php echo URLROOT; ?>/calendar/requestClubOwnership">
                            <i class="fas fa-crown me-2 text-warning"></i>Request Club Ownership
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="<?php echo URLROOT; ?>/calendar/import">
                            <i class="fas fa-upload me-2"></i>Import Events
                        </a></li>
                        <?php endif; ?>
                        <?php if (isAdmin()): ?>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="<?php echo URLROOT; ?>/calendar/settings">
                            <i class="fas fa-cog me-2"></i>Calendar Settings
                        </a></li>
                        <?php endif; ?>
                    </ul>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Main Calendar Column (Full Width) -->
        <div class="col-12 mb-4">
            <!-- Advanced Filter -->
            <?php include APPROOT . '/views/calendar/includes/advanced_filter.php'; ?>
            
            <!-- Monthly Event Chart Container -->
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white py-3">
                    <div class="row align-items-center">
                        <div class="col">
                            <h3 class="mb-0">
                                <i class="fas fa-chart-event me-2"></i>
                                <span id="eventMonthTitle">Loading...</span>
                            </h3>
                        </div>
                        <div class="col-auto">
                            <div class="btn-group">
                                <button type="button" class="btn btn-outline-light btn-sm" id="eventPrevMonth">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <button type="button" class="btn btn-outline-light btn-sm" id="eventToday">
                                    Today
                                </button>
                                <button type="button" class="btn btn-outline-light btn-sm" id="eventNextMonth">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Timezone Warning for Guests -->
                <?php if (!isLoggedIn()): ?>
                <div class="alert alert-info mb-0 border-0 rounded-0" style="border-top: 1px solid #dee2e6 !important;">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-clock me-2"></i>
                        <small class="mb-0">
                            <strong>Note:</strong> All event times are displayed in Eastern Time (New York).
                            <a href="<?php echo URLROOT; ?>/auth/register" class="alert-link">Register</a> or
                            <a href="<?php echo URLROOT; ?>/auth/login" class="alert-link">login</a> to see times in your local timezone.
                        </small>
                    </div>
                </div>
                <?php endif; ?>

                <div class="card-body p-0">
                    <!-- Desktop Event Chart -->
                    <div id="eventDesktop" class="event-desktop d-lg-block">
                        <!-- Weekly Event Container -->
                        <div id="eventContainer" class="event-weeks-container">
                            <div class="event-loading text-center py-5">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <div class="mt-3">
                                    <h5>Loading Weekly Event Chart...</h5>
                                    <p class="text-muted">Preparing weekly timeline view</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Event Table Below Calendar -->
            <div class="calendar-events-table" id="eventsTable" style="display: none;">
                <div class="calendar-events-table-header">
                    <h3 class="calendar-events-table-title">
                        <i class="fas fa-list me-2"></i>
                        Events List
                        <span id="eventsTableDateRange" class="text-muted ms-2"></span>
                    </h3>
                </div>
                <div class="calendar-events-table-content">
                    <table class="events-table">
                        <thead>
                            <tr>
                                <th>Event Title</th>
                                <th>City</th>
                                <th>State</th>
                                <th>Start Date/Time</th>
                                <th>End Date/Time</th>
                                <th>Venue</th>
                            </tr>
                        </thead>
                        <tbody id="eventsTableBody">
                            <!-- Events will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Event Hover Popup -->
<div class="event-hover-popup" id="eventHoverPopup">
    <h4 id="popupTitle"></h4>
    <div class="popup-detail" id="popupCalendarDetail" style="display: none;">
        <strong>Calendar:</strong> <span id="popupCalendar"></span>
    </div>
    <div class="popup-detail">
        <strong>Date:</strong> <span id="popupDate"></span>
    </div>
    <div class="popup-detail">
        <strong>Time:</strong> <span id="popupTime"></span>
    </div>
    <div class="popup-detail">
        <strong>Location:</strong> <span id="popupLocation"></span>
    </div>
    <div class="popup-detail">
        <strong>Venue:</strong> <span id="popupVenue"></span>
    </div>
    <div class="popup-description" id="popupDescription"></div>
    <div class="popup-clubs" id="popupClubs" style="display: none;">
        <div class="clubs-label">Hosted by:</div>
        <div class="clubs-list" id="popupClubsList"></div>
    </div>
    <!-- DEBUG: Club enhancement loaded v3.68.1 -->
    <div style="display: none;" id="clubEnhancementMarker">v3.68.1</div>
    <div id="popupActions" class="popup-actions mt-2" style="display: none;">
        <button type="button" class="btn btn-sm btn-primary me-2" id="popupViewBtn">View Details</button>
        <button type="button" class="btn btn-sm btn-secondary" id="popupEditBtn">Edit</button>
    </div>
</div>

<!-- Event Modal removed - events now navigate directly to details page -->



<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Enable debug mode for troubleshooting
        const DEBUG_MODE = <?php echo defined('DEBUG_MODE') && DEBUG_MODE ? 'true' : 'false'; ?>;
        
        if (DEBUG_MODE) {
            console.log('Initializing custom calendar...');
        }
        

        
        try {
            // Check if MonthlyEventChart class is available
            if (typeof MonthlyEventChart !== 'function') {
                const scriptElement = document.querySelector('script[src*="monthly-event-chart.js"]');
                console.error('MonthlyEventChart class not found.');
                console.error('Script element:', scriptElement);
                console.error('Script URL:', scriptElement?.src || 'Not found');
                console.error('All script elements:', document.querySelectorAll('script[src]'));
                
                throw new Error('MonthlyEventChart class not found. Make sure monthly-event-chart.js is loaded correctly.');
            }
            
            // Get calendar settings
            const settings = {
                defaultView: '<?php echo isset($data['settings']['default_view']) ? $data['settings']['default_view'] : 'month'; ?>',
                firstDayOfWeek: <?php echo isset($data['settings']['week_starts_on']) ? $data['settings']['week_starts_on'] : 0; ?>,
                timeFormat: '<?php echo isset($data['settings']['time_format']) ? $data['settings']['time_format'] : '12'; ?>',
                showWeekends: <?php echo isset($data['settings']['event_show_weekends']) && $data['settings']['event_show_weekends'] ? 'true' : 'false'; ?>,
                showTodayLine: <?php echo isset($data['settings']['event_show_today_line']) && $data['settings']['event_show_today_line'] ? 'true' : 'false'; ?>,
                showEventHover: <?php echo isset($data['settings']['event_show_event_hover']) && $data['settings']['event_show_event_hover'] ? 'true' : 'false'; ?>,
                businessHoursStart: '<?php echo isset($data['settings']['business_hours_start']) ? substr($data['settings']['business_hours_start'], 0, 5) : '09:00'; ?>',
                businessHoursEnd: '<?php echo isset($data['settings']['business_hours_end']) ? substr($data['settings']['business_hours_end'], 0, 5) : '17:00'; ?>',
                defaultEventDuration: <?php echo isset($data['settings']['default_event_duration']) ? $data['settings']['default_event_duration'] : 60; ?>,
                enableDragDrop: <?php echo isset($data['settings']['event_enable_drag_drop']) && $data['settings']['event_enable_drag_drop'] ? 'true' : 'false'; ?>,
                enableResize: false, // Resize not supported in Event chart
                maxEventsPerDay: 5,
                eventSources: [], // Start with no event sources - let the filter system handle all event loading
                userPermissions: {
                    isAdmin: <?php echo isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin' ? 'true' : 'false'; ?>,
                    isCoordinator: <?php echo isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'coordinator' ? 'true' : 'false'; ?>,
                    userId: <?php echo isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 'null'; ?>,
                    userRole: '<?php echo isset($_SESSION['user_role']) ? $_SESSION['user_role'] : 'guest'; ?>'
                },
                onEventClick: function(event) {
                    if (DEBUG_MODE) console.log('Event clicked:', event);
                    // Navigate directly to event details page instead of showing popup
                    window.location.href = '<?php echo URLROOT; ?>/calendar/event/' + event.id;
                },
                onDateClick: function(date) {
                    if (DEBUG_MODE) console.log('Date clicked:', date);
                    // Redirect to create event page with pre-filled date
                    window.location.href = '<?php echo URLROOT; ?>/calendar/createEvent?start=' + 
                        date.toISOString().slice(0, 16).replace('T', ' ') + 
                        '&end=' + new Date(date.getTime() + (settings.defaultEventDuration * 60 * 1000))
                            .toISOString().slice(0, 16).replace('T', ' ');
                },
                onEventDrop: function(event, newStart, newEnd) {
                    if (DEBUG_MODE) console.log('Event dropped:', event, newStart, newEnd);
                    updateEventDates(event.id, newStart, newEnd);
                },
                onEventResize: function(event, newStart, newEnd) {
                    if (DEBUG_MODE) console.log('Event resized:', event, newStart, newEnd);
                    updateEventDates(event.id, newStart, newEnd);
                },
                onViewChange: function(view) {
                    if (DEBUG_MODE) console.log('View changed:', view);
                }
            };
            
            if (DEBUG_MODE) {
                console.log('Event chart settings:', settings);
            }
            
            // Check if we have filter parameters in the URL
            const urlParams = new URLSearchParams(window.location.search);
            
            // Initialize Monthly Event Chart
            const eventChart = new MonthlyEventChart('eventDesktop', settings);
            
            // Make event chart instance available globally for the filter system
            window.calendar = eventChart; // Keep same name for compatibility with filters
            window.eventChart = eventChart;
            
            if (DEBUG_MODE) {
                console.log('Monthly Event Chart initialized successfully');
                // Make event instance available globally for debugging
                window.eventInstance = eventChart;
                
                // Verify filter system compatibility methods
                console.log('Filter system compatibility check:', {
                    loadEventsDirectly: typeof eventChart.loadEventsDirectly,
                    refetchEvents: typeof eventChart.refetchEvents,
                    clearEvents: typeof eventChart.clearEvents,
                    getEvents: typeof eventChart.getEvents,
                    loadEventsFromAPI: typeof eventChart.loadEventsFromAPI
                });
                
                // Test basic functionality
                console.log('=== EVENT CHART BASIC TEST ===');
                console.log('Current events count:', eventChart.getEvents().length);
                console.log('Current date:', eventChart.currentDate);
                console.log('=== END BASIC TEST ===');
            }
            
            // Apply URL parameters to filters if they exist
            if (urlParams.has('calendar_id')) {
                if (DEBUG_MODE) {
                    console.log('Found filter parameters in URL, applying them');
                }
                
                // If we have the calendar filter system, apply the URL parameters
                if (window.calendarFilters) {
                    // Parse the calendar_id parameter
                    const calendarIds = urlParams.get('calendar_id').split(',').filter(id => id.trim() !== '');
                    
                    if (DEBUG_MODE) {
                        console.log('Calendar IDs from URL:', calendarIds);
                    }
                    
                    // Update the calendar checkboxes
                    document.querySelectorAll('.calendar-checkbox').forEach(checkbox => {
                        checkbox.checked = calendarIds.includes(checkbox.value);
                    });
                    
                    // Update the filter state
                    window.calendarFilters.updateCalendarFilters();
                }
            }
            

            
            // CRITICAL: Apply initial filters to load events
            // The Event chart starts empty and relies on the filter system for ALL event loading
            console.log('=== INITIALIZATION: Preparing to apply initial filters ===');
            
            // Function to apply initial filters
            const applyInitialFilters = () => {
                if (window.calendarFilters && typeof window.calendarFilters.applyFilters === 'function') {
                    console.log('=== INITIALIZATION: Applying initial filters to load event chart events ===');
                    if (DEBUG_MODE) {
                        console.log('Event chart instance available:', !!eventChart);
                        console.log('Filter system available:', !!window.calendarFilters);
                        console.log('Current events in Event chart:', eventChart.events.length);
                    }
                    
                    // Ensure calendar filters are initialized first
                    if (typeof window.calendarFilters.updateCalendarFilters === 'function') {
                        window.calendarFilters.updateCalendarFilters();
                    }
                    
                    // Apply filters to load initial events
                    window.calendarFilters.applyFilters();
                    
                    if (DEBUG_MODE) {
                        console.log('=== INITIALIZATION: Initial filters applied ===');
                    }
                } else {
                    console.error('CRITICAL: Calendar filter system not available - Event chart will remain empty');
                    if (DEBUG_MODE) {
                        console.log('Available objects:', {
                            calendarFilters: !!window.calendarFilters,
                            applyFilters: window.calendarFilters ? typeof window.calendarFilters.applyFilters : 'N/A',
                            updateCalendarFilters: window.calendarFilters ? typeof window.calendarFilters.updateCalendarFilters : 'N/A'
                        });
                    }
                    
                    // Last resort: wait a bit longer and try again
                    setTimeout(() => {
                        if (window.calendarFilters && typeof window.calendarFilters.applyFilters === 'function') {
                            console.log('=== INITIALIZATION: Filter system now available, applying filters ===');
                            window.calendarFilters.applyFilters();
                        }
                    }, 500);
                }
            };
            
            // Apply initial filters with a small delay to ensure all systems are ready
            setTimeout(applyInitialFilters, 100);
            
            // Backup: If no events are loaded after a reasonable time, try again
            setTimeout(() => {
                if (eventChart && eventChart.events.length === 0) {
                    console.warn('=== BACKUP: No events loaded after initial attempt, retrying ===');
                    applyInitialFilters();
                }
            }, 1000);
            

            
            // Initialize event event table and enhanced popup functionality
            initializeEventEventTableAndPopup();
        
        
            // Initialize event event table and enhanced popup functionality
            function initializeEventEventTableAndPopup() {
                if (DEBUG_MODE) {
                    console.log('Initializing event event table and enhanced popup functionality');
                }
                
                // Show event table when event chart has events
                window.addEventListener('eventEventsLoaded', function(e) {
                    const events = e.detail.events;
                    updateEventEventTable(events);
                });
                
                // Initialize enhanced hover popup
                initializeEnhancedHoverPopup();
            }
            
            // Update event event table with current events
            function updateEventEventTable(events) {
                const tableBody = document.getElementById('eventEventsTableBody');
                const eventsTable = document.getElementById('eventEventsTable');
                const dateRangeSpan = document.getElementById('eventEventsTableDateRange');
                
                if (!tableBody || !eventsTable) return;
                
                // Clear existing rows
                tableBody.innerHTML = '';
                
                if (events && events.length > 0) {
                    // Sort events by start date
                    const sortedEvents = [...events].sort((a, b) => {
                        return new Date(a.start) - new Date(b.start);
                    });
                    
                    // Get date range - no locale conversion
                    const firstEvent = new Date(sortedEvents[0].start);
                    const lastEvent = new Date(sortedEvents[sortedEvents.length - 1].start);
                    const formatDateSimple = (date) => {
                        return (date.getMonth() + 1) + '/' + date.getDate() + '/' + date.getFullYear();
                    };
                    const dateRange = firstEvent.toDateString() === lastEvent.toDateString()
                        ? formatDateSimple(firstEvent)
                        : `${formatDateSimple(firstEvent)} - ${formatDateSimple(lastEvent)}`;
                    
                    dateRangeSpan.textContent = `(${dateRange})`;
                    
                    // Populate table rows with enhanced functionality
                    sortedEvents.forEach(event => {
                        const row = document.createElement('tr');
                        row.setAttribute('data-event-id', event.id);
                        row.classList.add('event-event-row');
                        
                        // Debug the date parsing for Las Vegas event
                        if (event.title && event.title.includes('Las Vegas')) {
                            console.log('=== LAS VEGAS EVENT DEBUG ===');
                            console.log('Event title:', event.title);
                            console.log('Raw event.start:', event.start);
                            console.log('Raw event.end:', event.end);
                        }

                        const startDate = new Date(event.start);
                        const endDate = new Date(event.end);

                        if (event.title && event.title.includes('Las Vegas')) {
                            console.log('Parsed startDate:', startDate);
                            console.log('startDate.getHours():', startDate.getHours());
                            console.log('startDate.toString():', startDate.toString());
                            console.log('=== END LAS VEGAS DEBUG ===');
                        }

                        const formatDateTime = (date) => {
                            return {
                                date: date.getFullYear() + '-' + String(date.getMonth() + 1).padStart(2, '0') + '-' + String(date.getDate()).padStart(2, '0'),
                                time: event.allDay ? 'All Day' : String(date.getHours()).padStart(2, '0') + ':' + String(date.getMinutes()).padStart(2, '0')
                            };
                        };
                        
                        const startFormatted = formatDateTime(startDate);
                        const endFormatted = formatDateTime(endDate);
                        
                        // Check user permissions for this event
                        const canEdit = checkEventEditPermission(event);
                        const actionsHtml = canEdit ? `
                            <button type="button" class="btn btn-sm btn-outline-primary me-1" onclick="editEvent('${event.id}')">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-info" onclick="viewEvent('${event.id}')">
                                <i class="fas fa-eye"></i>
                            </button>
                        ` : `
                            <button type="button" class="btn btn-sm btn-outline-info" onclick="viewEvent('${event.id}')">
                                <i class="fas fa-eye"></i>
                            </button>
                        `;
                        
                        row.innerHTML = `
                            <td class="event-title-cell">
                                <div class="d-flex align-items-center">
                                    <div class="event-color-indicator" style="background-color: ${event.backgroundColor || '#3788d8'};"></div>
                                    <span>${event.title || 'Untitled Event'}</span>
                                </div>
                            </td>
                            <td class="event-location-cell">${event.city || 'N/A'}</td>
                            <td class="event-location-cell">${event.state || 'N/A'}</td>
                            <td class="event-date-cell">${startFormatted.date}<br><small class="text-muted">${startFormatted.time}</small></td>
                            <td class="event-date-cell">${endFormatted.date}<br><small class="text-muted">${endFormatted.time}</small></td>
                            <td class="event-venue-cell">${event.venue || event.location || 'N/A'}</td>
                            <td class="text-center">${actionsHtml}</td>
                        `;
                        
                        // Add click handler
                        row.addEventListener('click', () => {
                            if (typeof window.calendar.options.onEventClick === 'function') {
                                window.calendar.options.onEventClick(event);
                            }
                        });
                        
                        // Add hover handlers for popup
                        addHoverHandlers(row, event);
                        
                        tableBody.appendChild(row);
                    });
                    
                    eventsTable.style.display = 'block';
                } else {
                    eventsTable.style.display = 'none';
                }
            }
            
            // Initialize enhanced hover popup functionality
            function initializeEnhancedHoverPopup() {
                console.log('=== CALENDAR: Initializing enhanced hover popup with clubs v3.68.1 ===');
                const popup = document.getElementById('eventHoverPopup');
                if (!popup) {
                    console.error('=== CALENDAR: eventHoverPopup element not found ===');
                    return;
                }
                
                let hoverTimeout;
                let hideTimeout;
                
                window.showEventEventPopup = function(event, targetElement, mouseX, mouseY) {
                    clearTimeout(hideTimeout);
                    
                    hoverTimeout = setTimeout(() => {
                        const startDate = new Date(event.start);
                        const endDate = new Date(event.end);
                        
                        // Populate popup content
                        document.getElementById('popupTitle').textContent = event.title || 'Untitled Event';
                        
                        // Handle calendar name display
                        const calendarName = event.extendedProps?.calendar_name || '';
                        const calendarDetail = document.getElementById('popupCalendarDetail');
                        if (calendarName && calendarName.trim() !== '') {
                            document.getElementById('popupCalendar').textContent = calendarName;
                            calendarDetail.style.display = 'block';
                        } else {
                            calendarDetail.style.display = 'none';
                        }
                        
                        // Enhanced date/time formatting for multi-day events - no locale conversion
                        const formatDate = (date) => {
                            const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                            return months[date.getMonth()] + ' ' + date.getDate() + ', ' + date.getFullYear();
                        };

                        const formatTime = (date) => {
                            const hours = date.getHours();
                            const minutes = date.getMinutes();
                            const ampm = hours >= 12 ? 'PM' : 'AM';
                            const displayHours = hours % 12 || 12;
                            return displayHours + ':' + String(minutes).padStart(2, '0') + ' ' + ampm;
                        };

                        if (event.allDay) {
                            document.getElementById('popupDate').textContent = startDate.toDateString() === endDate.toDateString()
                                ? formatDate(startDate)
                                : `${formatDate(startDate)} - ${formatDate(endDate)}`;
                            document.getElementById('popupTime').textContent = 'All Day';
                        } else {
                            // Check if it's a multi-day event
                            if (startDate.toDateString() === endDate.toDateString()) {
                                // Same day event
                                document.getElementById('popupDate').textContent = formatDate(startDate);
                                document.getElementById('popupTime').textContent = `${formatTime(startDate)} - ${formatTime(endDate)}`;
                            } else {
                                // Multi-day event - show complete date range
                                document.getElementById('popupDate').textContent = `${formatDate(startDate)} - ${formatDate(endDate)}`;
                                document.getElementById('popupTime').textContent = `${formatTime(startDate)} - ${formatTime(endDate)}`;
                            }
                        }
                        
                        document.getElementById('popupLocation').textContent = event.city && event.state 
                            ? `${event.city}, ${event.state}` 
                            : (event.location || 'N/A');
                        document.getElementById('popupVenue').textContent = event.venue || 'N/A';
                        document.getElementById('popupDescription').textContent = event.description || '';
                        
                        // Handle hosting clubs
                        const clubsContainer = document.getElementById('popupClubs');
                        const clubsList = document.getElementById('popupClubsList');
                        
                        const hostingClubs = event.extendedProps?.hosting_clubs || event.hosting_clubs || '';
                        
                        if (DEBUG_MODE) {
                            console.log('=== CALENDAR POPUP: Club data ===', {
                                eventId: event.id,
                                hostingClubs: hostingClubs,
                                extendedProps: event.extendedProps
                            });
                        }
                        
                        if (hostingClubs && hostingClubs.trim() !== '') {
                            // Parse the clubs data (format: "id:name||id:name||...")
                            const clubs = hostingClubs.split('||').filter(club => club.trim() !== '');
                            
                            if (clubs.length > 0) {
                                clubsList.innerHTML = '';
                                clubs.forEach(clubData => {
                                    const [clubId, clubName] = clubData.split(':');
                                    if (clubName && clubName.trim() !== '') {
                                        const clubBadge = document.createElement('span');
                                        clubBadge.className = 'club-badge';
                                        clubBadge.textContent = clubName.trim();
                                        clubBadge.title = `Hosted by ${clubName.trim()}`;
                                        clubsList.appendChild(clubBadge);
                                    }
                                });
                                clubsContainer.style.display = 'block';
                            } else {
                                clubsContainer.style.display = 'none';
                            }
                        } else {
                            clubsContainer.style.display = 'none';
                        }
                        
                        // Show/hide action buttons based on permissions
                        const canEdit = checkEventEditPermission(event);
                        const actionsDiv = document.getElementById('popupActions');
                        const editBtn = document.getElementById('popupEditBtn');
                        const viewBtn = document.getElementById('popupViewBtn');
                        
                        if (actionsDiv && editBtn && viewBtn) {
                            if (canEdit) {
                                actionsDiv.style.display = 'block';
                                editBtn.onclick = () => editEvent(event.id);
                                viewBtn.onclick = (e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    
                                    if (DEBUG_MODE) {
                                        console.log('=== CALENDAR: Popup View Details clicked ===', event.id);
                                    }
                                    
                                    viewEvent(event.id);
                                };
                            } else {
                                actionsDiv.style.display = 'none';
                            }
                        } else {
                            if (DEBUG_MODE) {
                                console.warn('=== CALENDAR: Popup action elements not found ===', {
                                    actionsDiv: !!actionsDiv,
                                    editBtn: !!editBtn,
                                    viewBtn: !!viewBtn
                                });
                            }
                        }
                        
                        // Position popup centered at mouse position
                        const popupRect = popup.getBoundingClientRect();
                        const viewportWidth = window.innerWidth;
                        const viewportHeight = window.innerHeight;
                        
                        let left = mouseX - (popupRect.width / 2);
                        let top = mouseY - popupRect.height - 10;
                        
                        // Adjust if popup goes off screen
                        if (left < 10) left = 10;
                        if (left + popupRect.width > viewportWidth - 10) left = viewportWidth - popupRect.width - 10;
                        if (top < 10) top = mouseY + 10;
                        
                        popup.style.left = `${left + window.scrollX}px`;
                        popup.style.top = `${top + window.scrollY}px`;
                        
                        popup.classList.add('show');
                    }, 500); // 500ms delay before showing
                };
                
                window.hideEventEventPopup = function() {
                    clearTimeout(hoverTimeout);
                    
                    hideTimeout = setTimeout(() => {
                        popup.classList.remove('show');
                    }, 300); // 300ms delay before hiding
                };
            }
            
            // Check if user can edit this event based on permissions
            function checkEventEditPermission(event) {
                if (!USER_ID) return false; // Not logged in
                if (IS_ADMIN) return true; // Admin can edit all events
                
                // Check if user owns this event
                if (event.extendedProps?.user_id && event.extendedProps.user_id == USER_ID) {
                    return true;
                }
                
                // Check if user is coordinator for this show/event
                if (IS_COORDINATOR && event.extendedProps?.show_id) {
                    // This would need to be enhanced with actual coordinator check
                    // For now, coordinators can edit events they're associated with
                    return event.extendedProps?.coordinators && 
                           event.extendedProps.coordinators.includes(USER_ID);
                }
                
                return false;
            }
            
            // Add enhanced hover handlers to elements
            function addHoverHandlers(element, event) {
                element.addEventListener('mouseenter', (e) => {
                    window.showEventEventPopup(event, element, e.clientX, e.clientY);
                });
                
                element.addEventListener('mouseleave', () => {
                    window.hideEventEventPopup();
                });
                
                // Add touch support for mobile
                element.addEventListener('touchstart', (e) => {
                    e.preventDefault();
                    const touch = e.touches[0];
                    window.showEventEventPopup(event, element, touch.clientX, touch.clientY);
                });
            }
            
            // Edit event function
            function editEvent(eventId) {
                window.location.href = `${URLROOT}/calendar/editEvent/${eventId}`;
            }
            
            // View event function
            function viewEvent(eventId) {
                window.location.href = `${URLROOT}/calendar/event/${eventId}`;
            }
            
            // Function to show event details - now redirects directly to event page
            function showEventDetails(event) {
                try {
                    if (DEBUG_MODE) {
                        console.log('Redirecting to event details:', event);
                    }
                    
                    // Navigate directly to event details page
                    window.location.href = '<?php echo URLROOT; ?>/calendar/event/' + event.id;
                    
                } catch (error) {
                    console.error('Error navigating to event details:', error);
                }
            }
            
            // Function to update event dates
            function updateEventDates(eventId, newStart, newEnd) {
                try {
                    if (DEBUG_MODE) {
                        console.log('Updating event dates:', eventId, newStart, newEnd);
                    }
                    
                    if (DEBUG_MODE) {
                        console.log('Loading upcoming events for calendars:', calendarIds);
                    }
                    
                    // If no calendars are selected, show message and return
                    if (calendarIds.length === 0) {
                        upcomingEventsEl.innerHTML = '<div class="p-3">No events to display. Please select at least one calendar.</div>';
                        return;
                    }
                    
                    // Get current date and 30 days in the future
                    const now = new Date();
                    const start = now.toISOString().slice(0, 10) + ' 00:00:00';
                    const end = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000).toISOString().slice(0, 10) + ' 23:59:59';
                    
                    // Build URL with base parameters
                    let url = `<?php echo URLROOT; ?>/calendar/getUpcomingEvents?start=${start}&end=${end}`;
                    
                    // Add filter parameters from the filter system if available
                    if (window.calendarFilters && typeof window.calendarFilters.getFilterParams === 'function') {
                        const filterParams = window.calendarFilters.getFilterParams();
                        if (filterParams) {
                            url += '&' + filterParams;
                        } else {
                            // If no filter params, use the calendar IDs
                            url += `&calendar_id=${calendarIds.join(',')}`;
                        }
                    } else {
                        // Fallback to old method if filter system is not available
                        url += `&calendar_id=${calendarIds.join(',')}`;
                    }
                    
                    // Add cache busting parameter to prevent stale event data
                    url += `&_cb=${Date.now()}`;
                    
                    if (DEBUG_MODE) {
                        console.log('Loading upcoming events from:', url);
                    }
                    
                    // Load upcoming events
                    fetch(url, {
                            headers: {
                                'Accept': 'application/json'
                            }
                        })
                        .then(response => {
                            if (!response.ok) {
                                throw new Error(`HTTP error! Status: ${response.status}`);
                            }
                            return response.json();
                        })
                        .then(events => {
                            if (DEBUG_MODE) {
                                console.log('Upcoming events loaded:', events);
                            }
                            
                            if (events.length === 0) {
                                upcomingEventsEl.innerHTML = '<div class="p-3">No upcoming events</div>';
                                return;
                            }
                            
                            let html = '';
                            
                            events.forEach(event => {
                                const eventDate = new Date(event.start);
                                const today = new Date();
                                today.setHours(0, 0, 0, 0);
                                
                                let dateText;
                                if (eventDate.toDateString() === today.toDateString()) {
                                    dateText = 'Today';
                                } else if (eventDate.toDateString() === new Date(today.getTime() + 86400000).toDateString()) {
                                    dateText = 'Tomorrow';
                                } else {
                                    const weekdays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
                                    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                                    dateText = weekdays[eventDate.getDay()] + ', ' + months[eventDate.getMonth()] + ' ' + eventDate.getDate();
                                }
                                
                                let timeText;
                                if (event.allDay) {
                                    timeText = 'All day';
                                } else {
                                    const hours = eventDate.getHours();
                                    const minutes = eventDate.getMinutes();
                                    const ampm = hours >= 12 ? 'PM' : 'AM';
                                    const displayHours = hours % 12 || 12;
                                    timeText = displayHours + ':' + String(minutes).padStart(2, '0') + ' ' + ampm;
                                }
                                
                                html += `
                                    <div class="upcoming-event" data-event-id="${event.id}">
                                        <div class="upcoming-event-date">${dateText}, ${timeText}</div>
                                        <div class="upcoming-event-title">${event.title}</div>
                                        <div class="upcoming-event-location">${event.location || ''}</div>
                                    </div>
                                `;
                            });
                            
                            upcomingEventsEl.innerHTML = html;
                            
                            // Add event listeners to upcoming events
                            document.querySelectorAll('.upcoming-event[data-event-id]').forEach(eventEl => {
                                eventEl.addEventListener('click', function() {
                                    const eventId = this.getAttribute('data-event-id');
                                    window.location.href = '<?php echo URLROOT; ?>/calendar/event/' + eventId;
                                });
                            });
                        })
                        .catch(error => {
                            console.error('Error loading upcoming events:', error);
                            upcomingEventsEl.innerHTML = '<div class="p-3 text-danger">Error loading events</div>';
                        });
                } catch (error) {
                    console.error('Error in loadUpcomingEvents:', error);
                }
            };
            

            
            // Function to update event dates
            function updateEventDates(eventId, newStart, newEnd) {
                try {
                    if (DEBUG_MODE) {
                        console.log('Updating event dates:', eventId, newStart, newEnd);
                    }
                    
                    // Format dates for API
                    const startStr = newStart.toISOString().slice(0, 19).replace('T', ' ');
                    const endStr = newEnd.toISOString().slice(0, 19).replace('T', ' ');
                    
                    // Send update request
                    fetch('<?php echo URLROOT; ?>/calendar/updateEventDates', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: `id=${eventId}&start_date=${startStr}&end_date=${endStr}&csrf_token=<?php echo generateCsrfToken(); ?>`
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! Status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (DEBUG_MODE) {
                            console.log('Event update response:', data);
                        }
                        
                        if (!data.success) {
                            alert('Error updating event: ' + data.message);
                            calendar.loadEvents(); // Reload events to revert changes
                        }
                    })
                    .catch(error => {
                        console.error('Error updating event:', error);
                        alert('Error updating event. Please try again.');
                        calendar.loadEvents(); // Reload events to revert changes
                    });
                } catch (error) {
                    console.error('Error in updateEventDates:', error);
                }
            }
        } catch (error) {
            console.error('Error initializing calendar:', error);
            
            // Check if it's a script loading issue
            let errorMessage = error.message;
            if (error.message.includes('MonthlyEventChart class not found')) {
                errorMessage += '<br><br><strong>Troubleshooting:</strong><ul>';
                errorMessage += '<li>Check if monthly-event-chart.js is accessible at: <code>' + URLROOT + '/public/js/monthly-event-chart.js</code></li>';
                errorMessage += '<li>Check browser console for any script loading errors</li>';
                errorMessage += '<li>Try refreshing the page (Ctrl+F5 or Cmd+Shift+R)</li>';
                errorMessage += '</ul>';
            }
            
            const eventDesktop = document.getElementById('eventDesktop');
            const eventMobile = document.getElementById('eventMobile');
            
            if (eventDesktop) {
                eventDesktop.innerHTML = `
                    <div class="alert alert-danger">
                        <strong>Error initializing Monthly Event Chart:</strong> ${errorMessage}
                        <br>
                        <small>Please try refreshing the page or contact support if the problem persists.</small>
                    </div>
                `;
            }
            
            if (eventMobile) {
                eventMobile.innerHTML = `
                    <div class="alert alert-danger">
                        <strong>Error initializing Mobile Event View:</strong> ${error.message}
                    </div>
                `;
            }
        }
        
        // Initialize the advanced filter system for Event chart
        if (window.calendarFilters) {
            // Apply filters button
            const applyFiltersBtn = document.getElementById('apply-filters');
            if (applyFiltersBtn) {
                applyFiltersBtn.addEventListener('click', function() {
                    // Use the calendarFilters.applyFilters function if available
                    if (window.calendarFilters && typeof window.calendarFilters.applyFilters === 'function') {
                        window.calendarFilters.applyFilters();
                    } else {
                        // Fallback to direct method if calendarFilters is not available
                        if (eventChart) {
                            // Try different methods to refresh the event chart
                            if (typeof eventChart.refetchEvents === 'function') {
                                eventChart.refetchEvents();
                            } else {
                                console.error('No method available to refresh event chart events');
                            }
                        }
                    }
                });
            }
        }
    });
</script>

<style>
/* Mobile-First Responsive Header Styles */
.header-nav-buttons {
    min-height: 44px; /* Minimum touch target size for mobile */
}

.header-nav-buttons .btn {
    min-height: 44px;
    font-size: 0.9rem;
    font-weight: 500;
    border-radius: 0.375rem;
    transition: all 0.2s ease-in-out;
}

.header-nav-buttons .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-nav-buttons .btn:active {
    transform: translateY(0);
}

/* Mobile button adjustments */
@media (max-width: 575.98px) {
    .header-nav-buttons .btn {
        font-size: 0.85rem;
        padding: 0.5rem 0.75rem;
    }
    
    .header-nav-buttons .btn-group {
        width: 100%;
    }
    
    .header-nav-buttons .btn-group .btn {
        flex: 1;
    }
    
    h1 {
        font-size: 1.75rem;
        margin-bottom: 0.5rem;
    }
}

/* Tablet adjustments */
@media (min-width: 576px) and (max-width: 991.98px) {
    .header-nav-buttons .btn {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
    }
}

/* Desktop adjustments */
@media (min-width: 992px) {
    .header-nav-buttons .btn {
        font-size: 0.95rem;
        padding: 0.5rem 1.25rem;
    }
}

/* Button group improvements */
.header-nav-buttons .btn-group .btn:first-child {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.header-nav-buttons .btn-group .btn:last-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.header-nav-buttons .btn-group .btn:not(:first-child):not(:last-child) {
    border-radius: 0;
}

/* Icon spacing improvements */
.header-nav-buttons .btn i {
    font-size: 0.9em;
}

/* Active state improvements */
.header-nav-buttons .btn.active {
    box-shadow: 0 2px 4px rgba(0,0,0,0.15);
}

/* Focus states for accessibility */
.header-nav-buttons .btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    outline: none;
}

/* Loading indicator styles */
.calendar-loading-indicator {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading {
    position: relative;
}

/* Event Popup Clubs Styling */
.popup-clubs {
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid #e9ecef;
}

.popup-clubs .clubs-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: #6c757d;
    margin-bottom: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.popup-clubs .clubs-list {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    align-items: center;
}

.popup-clubs .club-badge {
    display: inline-block;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #495057;
    font-size: 0.7rem;
    font-weight: 500;
    padding: 3px 8px;
    border-radius: 12px;
    border: 1px solid #dee2e6;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: default;
    white-space: nowrap;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

.popup-clubs .club-badge:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
    color: #212529;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-color: #adb5bd;
}

/* Responsive adjustments for clubs */
@media (max-width: 575.98px) {
    .popup-clubs .club-badge {
        font-size: 0.65rem;
        padding: 2px 6px;
    }
    
    .popup-clubs .clubs-label {
        font-size: 0.7rem;
    }
}

/* Monthly Chart Hover Popup Styling - Direct CSS to override Bootstrap */
.event-event-hover-popup {
    position: fixed !important;
    background: white !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 8px !important;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
    padding: 15px !important;
    max-width: 300px !important;
    z-index: 1000 !important;
    font-family: inherit !important;
}

.hover-popup-header h6 {
    margin: 0 0 10px 0 !important;
    font-size: 1.1rem !important;
    font-weight: 600 !important;
    color: #333 !important;
}

.hover-popup-body {
    font-size: 0.9rem !important;
    line-height: 1.4 !important;
}

.hover-popup-body div {
    margin-bottom: 5px !important;
    color: #666 !important;
}

.hover-popup-body i {
    color: #007bff !important;
    width: 16px !important;
    text-align: center !important;
}

/* Popup Clubs Styling for Monthly Chart Hover Popups - Direct CSS to override Bootstrap */
.popup-clubs-section {
    margin-top: 8px !important;
    padding-top: 8px !important;
    border-top: 1px solid #e9ecef !important;
}

.clubs-label-popup {
    font-size: 0.75rem !important;
    font-weight: 600 !important;
    color: #6c757d !important;
    margin-bottom: 4px !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

.clubs-list-popup {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 4px !important;
    align-items: center !important;
}

.club-badge-popup {
    display: inline-block !important;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    color: #495057 !important;
    font-size: 0.7rem !important;
    font-weight: 500 !important;
    padding: 3px 8px !important;
    border-radius: 12px !important;
    border: 1px solid #dee2e6 !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
    cursor: default !important;
    white-space: nowrap !important;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05) !important;
    margin: 2px !important;
}

.club-badge-popup:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%) !important;
    color: #212529 !important;
    text-decoration: none !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    border-color: #adb5bd !important;
}

/* Responsive adjustments for popup clubs - Bootstrap override */
@media (max-width: 575.98px) {
    .club-badge-popup {
        font-size: 0.65rem !important;
        padding: 2px 6px !important;
    }
    
    .clubs-label-popup {
        font-size: 0.7rem !important;
    }
}
</style>

<?php require APPROOT . '/views/includes/footer.php'; ?>