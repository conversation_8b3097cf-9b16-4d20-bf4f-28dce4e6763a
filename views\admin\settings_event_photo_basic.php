<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid">
    <!-- <PERSON> Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-cog me-2"></i>
                        Event Photo Basic Settings
                    </h1>
                    <p class="text-muted">Configure core event photo sharing settings</p>
                </div>
                <div>
                    <a href="<?php echo BASE_URL; ?>/admin/settings_event_photos" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i> Back to Event Photos
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Form -->
    <div class="row">
        <div class="col-md-8">
            <form method="POST" action="<?php echo BASE_URL; ?>/admin/updateEventPhotoBasicSettings">
                <input type="hidden" name="csrf_token" value="<?php echo $data['csrf_token']; ?>">
                
                <!-- System Control -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-power-off me-2"></i>
                            System Control
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="systemEnabled" name="enabled" 
                                           <?php echo ($data['settings']['enabled'] ?? true) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="systemEnabled">
                                        <strong>Enable Event Photo Sharing</strong>
                                    </label>
                                </div>
                                <small class="text-muted">Master switch to enable/disable the entire event photo sharing system</small>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="requireGPS" name="require_gps_verification" 
                                           <?php echo ($data['settings']['require_gps_verification'] ?? true) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="requireGPS">
                                        <strong>Require GPS Verification</strong>
                                    </label>
                                </div>
                                <small class="text-muted">Require users to be physically at events to upload photos</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Location Settings -->
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-map-marker-alt me-2"></i>
                            Location Settings
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label for="locationRadius" class="form-label">Location Radius</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="locationRadius" name="location_radius_miles" 
                                           value="<?php echo $data['settings']['location_radius_miles'] ?? 1.0; ?>" 
                                           min="0.1" max="10" step="0.1" required>
                                    <span class="input-group-text">miles</span>
                                </div>
                                <small class="text-muted">Distance from event location where photos can be uploaded</small>
                            </div>
                            
                            <div class="col-md-4">
                                <label for="timeBufferBefore" class="form-label">Time Before Event</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="timeBufferBefore" name="time_buffer_hours_before" 
                                           value="<?php echo $data['settings']['time_buffer_hours_before'] ?? 24; ?>" 
                                           min="0" max="168" required>
                                    <span class="input-group-text">hours</span>
                                </div>
                                <small class="text-muted">How many hours before event start photos are allowed</small>
                            </div>
                            
                            <div class="col-md-4">
                                <label for="timeBufferAfter" class="form-label">Time After Event</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="timeBufferAfter" name="time_buffer_hours_after" 
                                           value="<?php echo $data['settings']['time_buffer_hours_after'] ?? 24; ?>" 
                                           min="0" max="168" required>
                                    <span class="input-group-text">hours</span>
                                </div>
                                <small class="text-muted">How many hours after event end photos are allowed</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Upload Limits -->
                <div class="card mb-4">
                    <div class="card-header bg-warning text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-upload me-2"></i>
                            Upload Limits
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label for="maxPhotos" class="form-label">Max Photos per User per Event</label>
                                <input type="number" class="form-control" id="maxPhotos" name="max_photos_per_user_per_event" 
                                       value="<?php echo $data['settings']['max_photos_per_user_per_event'] ?? 50; ?>" 
                                       min="1" max="500" required>
                                <small class="text-muted">Maximum number of photos each user can upload per event</small>
                            </div>
                            
                            <div class="col-md-4">
                                <label for="maxFileSize" class="form-label">Max File Size</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="maxFileSize" name="max_file_size_mb" 
                                           value="<?php echo $data['settings']['max_file_size_mb'] ?? 10; ?>" 
                                           min="1" max="50" required>
                                    <span class="input-group-text">MB</span>
                                </div>
                                <small class="text-muted">Maximum file size for uploaded photos</small>
                            </div>
                            
                            <div class="col-md-4">
                                <label for="photoQuality" class="form-label">Photo Quality</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="photoQuality" name="photo_quality" 
                                           value="<?php echo $data['settings']['photo_quality'] ?? 85; ?>" 
                                           min="50" max="100" required>
                                    <span class="input-group-text">%</span>
                                </div>
                                <small class="text-muted">JPEG compression quality (higher = better quality, larger files)</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Privacy Settings -->
                <div class="card mb-4">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-shield-alt me-2"></i>
                            Privacy & Social Settings
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="defaultPrivacy" class="form-label">Default Privacy Level</label>
                                <select class="form-select" id="defaultPrivacy" name="default_privacy_level" required>
                                    <option value="public" <?php echo ($data['settings']['default_privacy_level'] ?? 'public') === 'public' ? 'selected' : ''; ?>>
                                        Public - Visible to everyone
                                    </option>
                                    <option value="attendees" <?php echo ($data['settings']['default_privacy_level'] ?? 'public') === 'attendees' ? 'selected' : ''; ?>>
                                        Attendees Only - Only event attendees can see
                                    </option>
                                    <option value="friends" <?php echo ($data['settings']['default_privacy_level'] ?? 'public') === 'friends' ? 'selected' : ''; ?>>
                                        Friends Only - Only user's friends can see
                                    </option>
                                    <option value="private" <?php echo ($data['settings']['default_privacy_level'] ?? 'public') === 'private' ? 'selected' : ''; ?>>
                                        Private - Only the uploader can see
                                    </option>
                                </select>
                                <small class="text-muted">Default privacy setting for new photos</small>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-check form-switch mt-4">
                                    <input class="form-check-input" type="checkbox" id="enableFacebookSharing" name="enable_facebook_sharing" 
                                           <?php echo ($data['settings']['enable_facebook_sharing'] ?? true) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="enableFacebookSharing">
                                        <strong>Enable Facebook Sharing</strong>
                                    </label>
                                </div>
                                <small class="text-muted">Allow users to share photos directly to Facebook</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Save Button -->
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-save me-2"></i>
                                    Save Basic Settings
                                </button>
                                <button type="reset" class="btn btn-outline-secondary btn-lg ms-2">
                                    <i class="fas fa-undo me-2"></i>
                                    Reset
                                </button>
                            </div>
                            <div class="text-muted">
                                <small>Changes take effect immediately</small>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- Help Sidebar -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-question-circle me-2"></i>
                        Help & Tips
                    </h5>
                </div>
                <div class="card-body">
                    <h6>Location Radius</h6>
                    <p class="small">Controls how close users must be to an event to upload photos. Smaller radius = more strict location verification.</p>
                    
                    <h6>Time Buffers</h6>
                    <p class="small">Allow photo uploads before/after official event times. Useful for setup/teardown photos.</p>
                    
                    <h6>Upload Limits</h6>
                    <p class="small">Prevent spam and manage storage by limiting photos per user and file sizes.</p>
                    
                    <h6>Privacy Levels</h6>
                    <p class="small">Control default visibility. Users can always change privacy on individual photos.</p>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        Recommended Settings
                    </h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li><strong>Location Radius:</strong> 1.0 miles</li>
                        <li><strong>Time Before:</strong> 24 hours</li>
                        <li><strong>Time After:</strong> 24 hours</li>
                        <li><strong>Max Photos:</strong> 50 per user</li>
                        <li><strong>Max File Size:</strong> 10 MB</li>
                        <li><strong>Photo Quality:</strong> 85%</li>
                        <li><strong>Default Privacy:</strong> Public</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.form-check-input:checked {
    background-color: #28a745;
    border-color: #28a745;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}
</style>

<?php require APPROOT . '/views/includes/footer.php'; ?>
