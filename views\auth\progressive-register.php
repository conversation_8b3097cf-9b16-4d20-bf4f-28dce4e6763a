<?php include 'views/includes/header.php'; ?>

<div class="progressive-registration-container">
    <div class="registration-card">
        <!-- Progress Indicator -->
        <div class="progress-indicator">
            <div class="progress-step active" data-step="1">
                <div class="step-circle">1</div>
                <span>Email</span>
            </div>
            <div class="progress-line"></div>
            <div class="progress-step" data-step="2">
                <div class="step-circle">2</div>
                <span>Account</span>
            </div>
            <div class="progress-line"></div>
            <div class="progress-step" data-step="3">
                <div class="step-circle">3</div>
                <span>Profile</span>
            </div>
        </div>

        <!-- Registration Header -->
        <div class="registration-header">
            <div class="header-icon">
                <i class="fas fa-car"></i>
            </div>
            <h2 id="stepTitle">Join the Car Community</h2>
            <p id="stepSubtitle">Connect with thousands of car enthusiasts nationwide</p>
        </div>

        <!-- Social Proof -->
        <div class="social-proof">
            <div class="proof-stats">
                <span class="stat-number" id="userCount">1,247+</span>
                <span class="stat-label">Members</span>
            </div>
            <div class="proof-stats">
                <span class="stat-number" id="showCount">89+</span>
                <span class="stat-label">Active Shows</span>
            </div>
        </div>

        <!-- Registration Form -->
        <form id="progressiveRegistrationForm" class="registration-form">
            <?php echo generateCsrfInput(); ?>
            <input type="hidden" name="source" value="<?php echo htmlspecialchars($data['conversion_source'] ?? 'direct'); ?>">
            
            <!-- Step 1: Email -->
            <div class="form-step active" data-step="1">
                <div class="form-group">
                    <label for="email">Email Address</label>
                    <input type="email" 
                           id="email" 
                           name="email" 
                           class="form-control" 
                           placeholder="<EMAIL>"
                           value="<?php echo htmlspecialchars($data['email'] ?? ''); ?>"
                           required>
                    <div class="form-feedback"></div>
                </div>
                
                <button type="button" class="btn btn-primary btn-lg btn-block" onclick="nextStep()">
                    Continue <i class="fas fa-arrow-right"></i>
                </button>
                
                <div class="alternative-signup">
                    <div class="divider">
                        <span>or</span>
                    </div>
                    <button type="button" class="btn btn-facebook btn-lg btn-block" onclick="signUpWithFacebook()">
                        <i class="fab fa-facebook-f"></i>
                        Continue with Facebook
                    </button>
                </div>
            </div>

            <!-- Step 2: Account Creation -->
            <div class="form-step" data-step="2">
                <div class="form-group">
                    <label for="name">Full Name</label>
                    <input type="text" 
                           id="name" 
                           name="name" 
                           class="form-control" 
                           placeholder="Your full name"
                           required>
                    <div class="form-feedback"></div>
                </div>
                
                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" 
                           id="password" 
                           name="password" 
                           class="form-control" 
                           placeholder="Choose a secure password"
                           minlength="6"
                           required>
                    <div class="form-feedback"></div>
                    <small class="form-text">Minimum 6 characters</small>
                </div>
                
                <button type="button" class="btn btn-primary btn-lg btn-block" onclick="nextStep()">
                    Create Account <i class="fas fa-user-plus"></i>
                </button>
                
                <button type="button" class="btn btn-link" onclick="previousStep()">
                    <i class="fas fa-arrow-left"></i> Back
                </button>
            </div>

            <!-- Step 3: Profile Completion (Optional) -->
            <div class="form-step" data-step="3">
                <div class="step-optional-header">
                    <h4>Almost Done! 🎉</h4>
                    <p>Help us personalize your experience (optional)</p>
                </div>
                
                <div class="form-group">
                    <label for="location">Location</label>
                    <input type="text" 
                           id="location" 
                           name="location" 
                           class="form-control" 
                           placeholder="City, State">
                    <small class="form-text">Help us show you nearby events</small>
                </div>
                
                <div class="form-group">
                    <label>Interests (select all that apply)</label>
                    <div class="interest-checkboxes">
                        <label class="interest-option">
                            <input type="checkbox" name="interests[]" value="classic">
                            <span class="checkmark"></span>
                            Classic Cars
                        </label>
                        <label class="interest-option">
                            <input type="checkbox" name="interests[]" value="muscle">
                            <span class="checkmark"></span>
                            Muscle Cars
                        </label>
                        <label class="interest-option">
                            <input type="checkbox" name="interests[]" value="imports">
                            <span class="checkmark"></span>
                            Import Cars
                        </label>
                        <label class="interest-option">
                            <input type="checkbox" name="interests[]" value="trucks">
                            <span class="checkmark"></span>
                            Trucks & SUVs
                        </label>
                        <label class="interest-option">
                            <input type="checkbox" name="interests[]" value="motorcycles">
                            <span class="checkmark"></span>
                            Motorcycles
                        </label>
                        <label class="interest-option">
                            <input type="checkbox" name="interests[]" value="racing">
                            <span class="checkmark"></span>
                            Racing
                        </label>
                    </div>
                </div>
                
                <button type="button" class="btn btn-primary btn-lg btn-block" onclick="completeRegistration()">
                    Complete Profile <i class="fas fa-check"></i>
                </button>
                
                <button type="button" class="btn btn-link" onclick="skipProfile()">
                    Skip for now
                </button>
            </div>
        </form>

        <!-- Loading State -->
        <div class="loading-overlay" id="loadingOverlay" style="display: none;">
            <div class="spinner"></div>
            <p>Creating your account...</p>
        </div>

        <!-- Footer -->
        <div class="registration-footer">
            <p>Already have an account? <a href="<?php echo BASE_URL; ?>/auth/login">Sign In</a></p>
            <p class="terms-text">
                By signing up, you agree to our 
                <a href="<?php echo BASE_URL; ?>/home/<USER>">Terms of Service</a> and 
                <a href="<?php echo BASE_URL; ?>/home/<USER>">Privacy Policy</a>
            </p>
        </div>
    </div>
</div>

<!-- Progressive Registration Styles -->
<style>
.progressive-registration-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #050372 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.registration-card {
    background: white;
    border-radius: 16px;
    padding: 40px;
    max-width: 500px;
    width: 100%;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    position: relative;
}

/* Progress Indicator */
.progress-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 30px;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    opacity: 0.5;
    transition: all 0.3s ease;
}

.progress-step.active {
    opacity: 1;
}

.progress-step.completed {
    opacity: 1;
}

.step-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    transition: all 0.3s ease;
}

.progress-step.active .step-circle {
    background: #dc3545;
    color: white;
}

.progress-step.completed .step-circle {
    background: #28a745;
    color: white;
}

.progress-line {
    width: 60px;
    height: 2px;
    background: #e9ecef;
    margin: 0 10px;
}

.progress-step span {
    font-size: 0.85rem;
    font-weight: 500;
    color: #666;
}

/* Registration Header */
.registration-header {
    text-align: center;
    margin-bottom: 25px;
}

.header-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #dc3545, #c82333);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: white;
    font-size: 2rem;
}

.registration-header h2 {
    color: #333;
    margin-bottom: 10px;
    font-weight: 700;
}

.registration-header p {
    color: #666;
    margin: 0;
}

/* Social Proof */
.social-proof {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin: 25px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
}

.proof-stats {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #dc3545;
}

.stat-label {
    font-size: 0.9rem;
    color: #666;
}

/* Form Steps */
.form-step {
    display: none;
}

.form-step.active {
    display: block;
    animation: stepFadeIn 0.3s ease-out;
}

@keyframes stepFadeIn {
    from { opacity: 0; transform: translateX(20px); }
    to { opacity: 1; transform: translateX(0); }
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.form-feedback {
    margin-top: 5px;
    font-size: 0.875rem;
}

.form-feedback.error {
    color: #dc3545;
}

.form-feedback.success {
    color: #28a745;
}

/* Buttons */
.btn {
    border-radius: 8px;
    padding: 12px 20px;
    font-weight: 600;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
}

.btn-block {
    width: 100%;
    display: block;
}

.btn-lg {
    padding: 14px 24px;
    font-size: 1.1rem;
}

.btn-primary {
    background: #dc3545;
    color: white;
}

.btn-primary:hover {
    background: #c82333;
    transform: translateY(-1px);
}

.btn-facebook {
    background: #1877f2;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.btn-facebook:hover {
    background: #166fe5;
}

.btn-link {
    background: none;
    color: #666;
    text-decoration: none;
    padding: 8px;
}

.btn-link:hover {
    color: #333;
    text-decoration: underline;
}

/* Alternative Signup */
.alternative-signup {
    margin-top: 25px;
}

.divider {
    text-align: center;
    margin: 20px 0;
    position: relative;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e9ecef;
}

.divider span {
    background: white;
    padding: 0 15px;
    color: #666;
    font-size: 0.9rem;
}

/* Interest Checkboxes */
.interest-checkboxes {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
}

.interest-option {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.interest-option:hover {
    background: #f8f9fa;
}

.interest-option input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #ddd;
    border-radius: 4px;
    position: relative;
    transition: all 0.2s ease;
}

.interest-option input[type="checkbox"]:checked + .checkmark {
    background: #dc3545;
    border-color: #dc3545;
}

.interest-option input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: -2px;
    left: 2px;
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* Loading Overlay */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 16px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #dc3545;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Registration Footer */
.registration-footer {
    text-align: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.registration-footer p {
    margin: 10px 0;
    color: #666;
    font-size: 0.9rem;
}

.registration-footer a {
    color: #dc3545;
    text-decoration: none;
}

.registration-footer a:hover {
    text-decoration: underline;
}

.terms-text {
    font-size: 0.8rem !important;
}

/* Step Optional Header */
.step-optional-header {
    text-align: center;
    margin-bottom: 25px;
}

.step-optional-header h4 {
    color: #333;
    margin-bottom: 8px;
}

.step-optional-header p {
    color: #666;
    margin: 0;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .registration-card {
        padding: 30px 20px;
        margin: 10px;
    }
    
    .progress-indicator {
        margin-bottom: 20px;
    }
    
    .progress-line {
        width: 40px;
    }
    
    .social-proof {
        gap: 20px;
    }
    
    .interest-checkboxes {
        grid-template-columns: 1fr;
    }
}
</style>

<!-- Progressive Registration JavaScript -->
<script>
let currentStep = 1;
let formData = {};

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    loadSocialProofStats();
    setupFormValidation();
});

// Load social proof statistics
function loadSocialProofStats() {
    fetch('<?php echo BASE_URL; ?>/progressive_auth/getStats')
        .then(response => response.json())
        .then(data => {
            if (data.users) {
                document.getElementById('userCount').textContent = data.users.toLocaleString() + '+';
            }
            if (data.shows) {
                document.getElementById('showCount').textContent = data.shows + '+';
            }
        })
        .catch(error => console.log('Stats loading failed:', error));
}

// Setup form validation
function setupFormValidation() {
    const emailInput = document.getElementById('email');
    if (emailInput) {
        emailInput.addEventListener('blur', validateEmail);
        emailInput.addEventListener('input', clearFeedback);
    }
}

// Validate email
function validateEmail() {
    const email = document.getElementById('email').value;
    const feedback = document.querySelector('[data-step="1"] .form-feedback');
    
    if (!email) {
        showFeedback(feedback, '', '');
        return;
    }
    
    if (!isValidEmail(email)) {
        showFeedback(feedback, 'Please enter a valid email address', 'error');
        return;
    }
    
    // Check email availability
    fetch('<?php echo BASE_URL; ?>/progressive_auth/checkEmail', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'email=' + encodeURIComponent(email) + '&' + getCsrfToken()
    })
    .then(response => response.json())
    .then(data => {
        if (data.available) {
            showFeedback(feedback, 'Email available ✓', 'success');
        } else {
            showFeedback(feedback, 'This email is already registered. <a href="<?php echo BASE_URL; ?>/auth/login">Sign in instead?</a>', 'error');
        }
    })
    .catch(error => {
        console.log('Email check failed:', error);
    });
}

// Show feedback message
function showFeedback(element, message, type) {
    element.innerHTML = message;
    element.className = 'form-feedback ' + type;
}

// Clear feedback
function clearFeedback(event) {
    const feedback = event.target.closest('.form-group').querySelector('.form-feedback');
    if (feedback) {
        feedback.innerHTML = '';
        feedback.className = 'form-feedback';
    }
}

// Move to next step
function nextStep() {
    if (currentStep === 1) {
        processStepOne();
    } else if (currentStep === 2) {
        processStepTwo();
    }
}

// Move to previous step
function previousStep() {
    if (currentStep > 1) {
        currentStep--;
        updateStepDisplay();
    }
}

// Process step one
function processStepOne() {
    const email = document.getElementById('email').value;
    
    if (!email || !isValidEmail(email)) {
        showFeedback(document.querySelector('[data-step="1"] .form-feedback'), 'Please enter a valid email address', 'error');
        return;
    }
    
    showLoading(true);
    
    fetch('<?php echo BASE_URL; ?>/progressive_auth/processStep', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'step=1&email=' + encodeURIComponent(email) + '&' + getCsrfToken()
    })
    .then(response => response.json())
    .then(data => {
        showLoading(false);
        
        if (data.success) {
            formData.email = email;
            currentStep = 2;
            updateStepDisplay();
            updateStepContent();
        } else {
            showFeedback(document.querySelector('[data-step="1"] .form-feedback'), data.error, 'error');
        }
    })
    .catch(error => {
        showLoading(false);
        showFeedback(document.querySelector('[data-step="1"] .form-feedback'), 'Something went wrong. Please try again.', 'error');
    });
}

// Process step two
function processStepTwo() {
    const name = document.getElementById('name').value;
    const password = document.getElementById('password').value;
    
    if (!name || !password) {
        showFeedback(document.querySelector('[data-step="2"] .form-feedback'), 'Please fill in all fields', 'error');
        return;
    }
    
    if (password.length < 6) {
        showFeedback(document.querySelector('[data-step="2"] .form-feedback'), 'Password must be at least 6 characters', 'error');
        return;
    }
    
    showLoading(true);
    
    const formData = new FormData();
    formData.append('step', '2');
    formData.append('name', name);
    formData.append('password', password);
    formData.append('source', document.querySelector('[name="source"]').value);
    
    // Add CSRF token
    const csrfToken = document.querySelector('[name="csrf_token"]');
    if (csrfToken) {
        formData.append('csrf_token', csrfToken.value);
    }
    
    fetch('<?php echo BASE_URL; ?>/progressive_auth/processStep', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        showLoading(false);
        
        if (data.success) {
            if (data.redirect) {
                window.location.href = data.redirect;
            } else {
                currentStep = 3;
                updateStepDisplay();
                updateStepContent();
            }
        } else {
            showFeedback(document.querySelector('[data-step="2"] .form-feedback'), data.error, 'error');
        }
    })
    .catch(error => {
        showLoading(false);
        showFeedback(document.querySelector('[data-step="2"] .form-feedback'), 'Something went wrong. Please try again.', 'error');
    });
}

// Complete registration
function completeRegistration() {
    const location = document.getElementById('location').value;
    const interests = Array.from(document.querySelectorAll('[name="interests[]"]:checked')).map(cb => cb.value);
    
    showLoading(true);
    
    const formData = new FormData();
    formData.append('step', '3');
    formData.append('location', location);
    interests.forEach(interest => formData.append('interests[]', interest));
    
    // Add CSRF token
    const csrfToken = document.querySelector('[name="csrf_token"]');
    if (csrfToken) {
        formData.append('csrf_token', csrfToken.value);
    }
    
    fetch('<?php echo BASE_URL; ?>/progressive_auth/processStep', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        showLoading(false);
        
        if (data.success && data.redirect) {
            window.location.href = data.redirect;
        } else {
            window.location.href = '<?php echo BASE_URL; ?>/user/dashboard';
        }
    })
    .catch(error => {
        showLoading(false);
        window.location.href = '<?php echo BASE_URL; ?>/user/dashboard';
    });
}

// Skip profile completion
function skipProfile() {
    window.location.href = '<?php echo BASE_URL; ?>/user/dashboard';
}

// Sign up with Facebook
function signUpWithFacebook() {
    window.location.href = '<?php echo BASE_URL; ?>/auth/facebook';
}

// Update step display
function updateStepDisplay() {
    // Update progress indicator
    document.querySelectorAll('.progress-step').forEach((step, index) => {
        const stepNum = index + 1;
        step.classList.remove('active', 'completed');
        
        if (stepNum < currentStep) {
            step.classList.add('completed');
        } else if (stepNum === currentStep) {
            step.classList.add('active');
        }
    });
    
    // Update form steps
    document.querySelectorAll('.form-step').forEach((step, index) => {
        const stepNum = index + 1;
        step.classList.remove('active');
        
        if (stepNum === currentStep) {
            step.classList.add('active');
        }
    });
}

// Update step content
function updateStepContent() {
    const titles = {
        1: 'Join the Car Community',
        2: 'Create Your Account',
        3: 'Complete Your Profile'
    };
    
    const subtitles = {
        1: 'Connect with thousands of car enthusiasts nationwide',
        2: 'Just a few more details to get started',
        3: 'Help us personalize your experience'
    };
    
    document.getElementById('stepTitle').textContent = titles[currentStep];
    document.getElementById('stepSubtitle').textContent = subtitles[currentStep];
}

// Show/hide loading
function showLoading(show) {
    document.getElementById('loadingOverlay').style.display = show ? 'flex' : 'none';
}

// Utility functions
function isValidEmail(email) {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
}

function getCsrfToken() {
    const token = document.querySelector('[name="csrf_token"]');
    return token ? 'csrf_token=' + encodeURIComponent(token.value) : '';
}
</script>

<?php include 'views/includes/footer.php'; ?>
