/**
 * PWA Features CSS for Rowan Elite Rides Events & Shows
 * Styles for PWA-specific components and enhanced mobile features
 * 
 * CAMERA LAYERING FIX APPLIED:
 * - Camera modal z-index increased from 1070 to 999998
 * - QR scanner modal z-index increased from 1070 to 999998  
 * - Video elements (#camera-video, #qr-video) z-index set to 999999
 * - This ensures camera video renders above navigation elements (z-index: 999999)
 */

/* PWA Install Button */
.pwa-install-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1050;
    background: #FFD700 !important;
    color: #1338BE !important;
    border: 2px solid #1338BE !important;
    border-radius: 50px !important;
    padding: 12px 20px !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4) !important;
    transition: all 0.3s ease;
    display: none;
}

.pwa-install-btn:hover {
    transform: translateY(-2px);
    background: #FFC107 !important;
    color: #1338BE !important;
    border-color: #1338BE !important;
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.6) !important;
}

.pwa-install-btn i {
    margin-right: 8px;
}

/* Online Status Indicator */
.online-indicator {
    position: fixed;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 1060;
    transition: all 0.3s ease;
}

.online-indicator.online {
    background: #28a745;
    color: white;
}

.online-indicator.offline {
    background: #1338BE;
    color: white;
    animation: pulse 2s infinite;
}

/* Notification Prompt */
.notification-prompt {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1055;
    margin: 0;
    border-radius: 0;
    border: none;
    border-bottom: 3px solid #1338BE;
}

/* Update Banner */
.update-banner {
    position: fixed !important;
    z-index: 9999 !important;
    display: block !important; /* Ensure it's visible on screen */
    /* Let JavaScript handle positioning with inline styles */
}

/* Ensure update banner is visible on screen (not print) */
@media screen {
    .update-banner {
        display: block !important;
    }
}

/* Toast Container */
.toast-container {
    z-index: 1060;
}

.toast {
    min-width: 300px;
    margin-bottom: 10px;
    border: none;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.toast-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
}

.toast-success {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    color: white;
}

.toast-warning {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529;
}

.toast-error {
    background: linear-gradient(135deg, #1338BE, #0f2a9a);
    color: white;
}

.toast-header {
    background: transparent;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    color: inherit;
}

.toast-body {
    color: inherit;
}

/* Camera modal backdrop to block page content */
.camera-modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 1.0);
    z-index: 999997;
    pointer-events: none;
}

/* Camera Modal - Portrait layout with banner space */
.camera-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw;
    height: 100vh;
    background:
        linear-gradient(to bottom,
            rgba(0, 0, 0, 0.95) 0%,
            rgba(0, 0, 0, 0.95) 13%,
            rgba(0, 0, 0, 0.1) 13%,
            rgba(0, 0, 0, 0.1) 85%,
            rgba(0, 0, 0, 0.95) 85%,
            rgba(0, 0, 0, 0.95) 100%);
    z-index: 999998;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    touch-action: none;
}

.camera-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
}

/* Camera banner at top */
.camera-banner {
    height: 13vh;
    width: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 2px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.camera-banner img {
    height: 95%;
    width: auto;
    object-fit: contain;
}

.camera-banner .banner-text {
    color: white;
    text-align: center;
    font-size: 1.1rem;
    font-weight: 500;
    padding: 10px;
}

/* Logo banner specific styling */
.camera-banner .logo-banner {
    height: 95%;
    width: auto;
    object-fit: contain;
    display: block;
    margin: 0 auto;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
}

/* Camera viewfinder area */
.camera-viewfinder {
    flex: 1;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

#camera-video {
    width: 100%;
    height: 70%;
    object-fit: cover;
    border-radius: 10px;
    max-width: 500px;
}

.camera-controls {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 20px;
}

.camera-controls .btn {
    border-radius: 50px;
    padding: 12px 24px;
    font-weight: 600;
    min-width: 120px;
    font-size: 1.1rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.camera-controls .btn:hover,
.camera-controls .btn:focus {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

.camera-controls .btn-danger {
    background: linear-gradient(135deg, #1338BE, #0f2a9a);
    border: none;
}

.camera-controls .btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
}

/* QR Scanner Modal - Portrait layout with banner space */
.qr-scanner-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw;
    height: 100vh;
    background:
        linear-gradient(to bottom,
            rgba(0, 0, 0, 0.95) 0%,
            rgba(0, 0, 0, 0.95) 13%,
            rgba(0, 0, 0, 0.1) 13%,
            rgba(0, 0, 0, 0.1) 85%,
            rgba(0, 0, 0, 0.95) 85%,
            rgba(0, 0, 0, 0.95) 100%);
    z-index: 999998;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    touch-action: none;
}

.qr-scanner-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
}

/* QR Scanner banner at top */
.qr-banner {
    height: 13vh;
    width: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 2px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.qr-banner img {
    height: 95%;
    width: auto;
    object-fit: contain;
}

.qr-banner .banner-text {
    color: white;
    text-align: center;
    font-size: 1.1rem;
    font-weight: 500;
    padding: 10px;
}

/* Logo banner specific styling for QR */
.qr-banner .logo-banner {
    height: 95%;
    width: auto;
    object-fit: contain;
    display: block;
    margin: 0 auto;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
}

/* QR Scanner viewfinder area */
.qr-viewfinder {
    flex: 1;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

#qr-video {
    width: 100%;
    height: 70%;
    object-fit: cover;
    border-radius: 10px;
    max-width: 400px;
}

.qr-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 250px;
    height: 250px;
    pointer-events: none;
}

.qr-target {
    width: 100%;
    height: 100%;
    border: 3px solid #00FF00;
    border-radius: 10px;
    position: relative;
    animation: qr-scan 2s infinite;
    box-shadow: 0 0 20px rgba(0, 255, 0, 0.5);
}

.qr-target::before,
.qr-target::after {
    content: '';
    position: absolute;
    width: 30px;
    height: 30px;
    border: 4px solid #00FF00;
    box-shadow: 0 0 10px rgba(0, 255, 0, 0.7);
}

.qr-target::before {
    top: -4px;
    left: -4px;
    border-right: none;
    border-bottom: none;
}

.qr-target::after {
    bottom: -4px;
    right: -4px;
    border-left: none;
    border-top: none;
}

@keyframes qr-scan {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.qr-controls {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
}



.qr-instructions {
    color: white;
    margin-top: 15px;
    font-size: 1.1rem;
}

/* Enhanced Mobile Navigation */
.mobile-nav-bottom {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
    border-top: 2px solid #1338BE;
    padding: 10px 0;
    z-index: 1050;
    display: none;
}

.mobile-nav-bottom.show {
    display: block;
}

.mobile-nav-items {
    display: flex;
    justify-content: space-around;
    align-items: center;
    max-width: 500px;
    margin: 0 auto;
}

.mobile-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: #cccccc;
    transition: all 0.3s ease;
    padding: 8px 12px;
    border-radius: 10px;
    min-width: 60px;
}

.mobile-nav-item:hover,
.mobile-nav-item.active {
    color: #1338BE;
    background: rgba(19, 56, 190, 0.1);
    transform: translateY(-2px);
}

.mobile-nav-item i {
    font-size: 1.2rem;
    margin-bottom: 4px;
}

.mobile-nav-item span {
    font-size: 0.7rem;
    font-weight: 600;
}

/* Dashboard Image with Chrome Circle - Circle fits behind image */
.mobile-nav-item.center-dashboard {
    position: relative !important;
    transform: translateY(-2px) !important;
    background: radial-gradient(circle, #2d2d2d, #1a1a1a) !important;
    border: 3px solid #c0c0c0 !important;
    border-radius: 50% !important;
    width: auto !important;
    height: auto !important;
    min-width: auto !important;
    max-width: auto !important;
    min-height: auto !important;
    max-height: auto !important;
    padding: 0 8px !important;
    margin: 0 5px !important;
    box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.4),
        inset 0 2px 4px rgba(255, 255, 255, 0.1),
        0 0 0 1px rgba(192, 192, 192, 0.3) !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
    overflow: visible !important;
    -webkit-border-radius: 50% !important;
    -moz-border-radius: 50% !important;
    outline: none !important;
    box-sizing: border-box !important;
}

.mobile-nav-item.center-dashboard:hover,
.mobile-nav-item.center-dashboard.active {
    background: radial-gradient(circle, #1338BE, #0d2a8a) !important;
    border: 3px solid #00ffff !important;
    border-radius: 50% !important;
    box-shadow:
        0 6px 12px rgba(19, 56, 190, 0.5),
        inset 0 2px 4px rgba(255, 255, 255, 0.2),
        0 0 0 2px rgba(0, 255, 255, 0.4),
        0 0 15px rgba(0, 255, 255, 0.3) !important;
    transform: translateY(-4px) !important;
    color: #ffffff !important;
}

.dashboard-image {
    width: auto !important;
    height: auto !important;
    max-width: none !important;
    max-height: none !important;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.6)) !important;
    transition: all 0.3s ease !important;
    margin-top: 1px !important;
}

.mobile-nav-item.center-dashboard:hover .dashboard-image,
.mobile-nav-item.center-dashboard.active .dashboard-image {
    filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.8)) brightness(1.1) !important;
    transform: none !important;
}

/* Hide the text span since image has text */
.mobile-nav-item.center-dashboard span {
    display: none !important;
}

/* Subtle pulse animation for the speedometer */
@keyframes subtle-pulse {
    0%, 100% {
        transform: scale(1) !important;
        filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.4)) !important;
    }
    50% {
        transform: scale(1.08) !important;
        filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.5)) !important;
    }
}

/* Circular chrome bezel for all other nav items */
.mobile-nav-item:not(.center-dashboard) {
    background: radial-gradient(circle, #2d2d2d, #1a1a1a) !important;
    border: 3px solid #c0c0c0 !important;
    border-radius: 50% !important;
    width: 60px !important;
    height: 60px !important;
    min-width: 60px !important;
    max-width: 60px !important;
    min-height: 60px !important;
    max-height: 60px !important;
    padding: 0 !important;
    margin: 0 3px !important;
    box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.4),
        inset 0 2px 4px rgba(255, 255, 255, 0.1),
        0 0 0 1px rgba(192, 192, 192, 0.3) !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    transition: all 0.3s ease !important;
    text-decoration: none !important;
    overflow: hidden !important;
    -webkit-border-radius: 50% !important;
    -moz-border-radius: 50% !important;
    outline: none !important;
    box-sizing: border-box !important;
}

.mobile-nav-item:not(.center-dashboard):hover,
.mobile-nav-item:not(.center-dashboard).active {
    background: radial-gradient(circle, #1338BE, #0d2a8a) !important;
    border: 3px solid #00ffff !important;
    border-radius: 50% !important;
    box-shadow:
        0 6px 12px rgba(19, 56, 190, 0.5),
        inset 0 2px 4px rgba(255, 255, 255, 0.2),
        0 0 0 2px rgba(0, 255, 255, 0.4),
        0 0 15px rgba(0, 255, 255, 0.3) !important;
    transform: translateY(-2px) !important;
    color: #ffffff !important;
    width: 60px !important;
    height: 60px !important;
}

/* Keep icons same size in the round buttons */
.mobile-nav-item:not(.center-dashboard) i {
    font-size: 1.2rem !important;
    margin-bottom: 2px !important;
    color: #cccccc !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8) !important;
}

.mobile-nav-item:not(.center-dashboard) span {
    font-size: 0.65rem !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.3px !important;
    margin-top: 1px !important;
    color: #cccccc !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8) !important;
    line-height: 1 !important;
}

/* Override any conflicting styles */
.mobile-nav-bottom .mobile-nav-item.center-dashboard {
    min-width: 80px !important;
    max-width: 80px !important;
}

/* Ensure proper spacing for the cluster effect */
.mobile-nav-items {
    display: flex !important;
    justify-content: space-around !important;
    align-items: flex-end !important; /* Align to bottom so center button appears raised */
    max-width: 500px !important;
    margin: 0 auto !important;
    padding: 0 10px !important;
}

/* Mobile notification badge */
.mobile-notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    min-width: 18px;
    height: 18px;
    font-size: 0.6rem;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #1a1a1a;
    z-index: 10;
    animation: badge-appear 0.3s ease-out;
}

/* Badge pulse animation for new notifications */
.badge-pulse {
    animation: badge-pulse 1s ease-in-out;
}

@keyframes badge-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); box-shadow: 0 0 15px rgba(220, 53, 69, 0.6); }
    100% { transform: scale(1); }
}

@keyframes badge-appear {
    0% { transform: scale(0); opacity: 0; }
    100% { transform: scale(1); opacity: 1; }
}

/* Mobile bell icon with unread messages */
.mobile-nav-item .fa-bell.has-unread {
    color: #FFD700 !important;
    text-shadow: 0 0 8px rgba(255, 215, 0, 0.6);
    animation: bell-glow 2s ease-in-out infinite alternate;
}

@keyframes bell-glow {
    0% { text-shadow: 0 0 8px rgba(255, 215, 0, 0.6); }
    100% { text-shadow: 0 0 12px rgba(255, 215, 0, 0.8); }
}

/* Quick Actions Floating Button */
.quick-actions-fab {
    position: fixed;
    bottom: 80px;
    right: 20px;
    z-index: 1049;
}

.fab-main {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: linear-gradient(135deg, #dc3545, #c82333);
    border: none;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
    transition: all 0.3s ease;
    cursor: pointer;
}

.fab-main:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.6);
}

.fab-actions {
    position: absolute;
    bottom: 70px;
    right: 0;
    display: flex;
    flex-direction: column;
    gap: 10px;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
    pointer-events: none;
}

.fab-actions.show {
    opacity: 1;
    transform: translateY(0);
    pointer-events: all;
}

.fab-action {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: #ffffff;
    border: 2px solid #1338BE;
    color: #1338BE;
    font-size: 1.2rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.fab-action:hover {
    background: #1338BE;
    color: white;
    transform: scale(1.1);
}

/* Offline Queue Indicator */
.offline-queue {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background: #ffc107;
    color: #212529;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    z-index: 1050;
    display: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.offline-queue:hover {
    background: #e0a800;
    transform: translateY(-2px);
}

.offline-queue.show {
    display: block;
}

.offline-queue i {
    margin-right: 6px;
    animation: spin 2s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Biometric Login Button */
.biometric-login {
    background: linear-gradient(135deg, #6f42c1, #5a32a3);
    border: none;
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    margin-top: 10px;
}

.biometric-login:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(111, 66, 193, 0.3);
}

.biometric-login i {
    margin-right: 8px;
}

/* Share Button */
.share-button {
    background: #17a2b8;
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.share-button:hover {
    background: #138496;
    transform: translateY(-1px);
}

.share-button i {
    margin-right: 6px;
}

/* PWA Splash Screen Styles */
@media (display-mode: standalone) {
    body {
        padding-top: env(safe-area-inset-top);
        padding-bottom: env(safe-area-inset-bottom);
    }
    
    .mobile-nav-bottom.show {
        padding-bottom: calc(10px + env(safe-area-inset-bottom));
    }
    
    .pwa-install-btn {
        display: none !important;
    }
}

/* Desktop Design - Hide FAB on larger screens */
@media (min-width: 992px) {
    .quick-actions-fab {
        display: none !important;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .mobile-nav-bottom {
        display: block;
    }
    
    .pwa-install-btn {
        bottom: 90px;
    }
    
    .quick-actions-fab {
        bottom: 150px;
    }
    
    .toast {
        min-width: 280px;
        margin: 5px;
    }
    
    .camera-controls,
    .qr-controls {
        flex-direction: column;
        gap: 10px;
    }
    
    .camera-controls .btn,
    .qr-controls .btn {
        min-width: 100px;
    }
}

@media (max-width: 480px) {
    .mobile-nav-item {
        min-width: 50px;
        padding: 6px 8px;
    }
    
    .mobile-nav-item i {
        font-size: 1rem;
    }
    
    .mobile-nav-item span {
        font-size: 0.6rem;
    }
    
    .fab-main {
        width: 48px;
        height: 48px;
        font-size: 1.3rem;
    }
    
    .fab-action {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .toast-warning {
        background: linear-gradient(135deg, #856404, #6c5300);
        color: white;
    }
    
    .fab-action {
        background: #2d2d2d;
        border-color: #dc3545;
        color: #dc3545;
    }

    .fab-action:hover {
        background: #dc3545;
        color: white;
    }
}

/* Print Styles */
@media print {
    .pwa-install-btn,
    .online-indicator,
    .notification-prompt,
    .update-banner,
    .mobile-nav-bottom,
    .quick-actions-fab,
    .offline-queue,
    .toast-container {
        display: none !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .pwa-install-btn,
    .fab-main {
        border: 2px solid currentColor;
    }
    
    .mobile-nav-item:hover,
    .mobile-nav-item.active {
        border: 1px solid currentColor;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .pwa-install-btn,
    .fab-main,
    .fab-action,
    .mobile-nav-item,
    .toast,
    .offline-queue {
        transition: none;
    }
    
    .online-indicator.offline,
    .qr-target,
    .offline-queue i {
        animation: none;
    }
}

/* Facebook Login Button States */
#facebook-login-btn:disabled {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    pointer-events: none !important;
    background-color: #6c757d !important;
    border-color: #6c757d !important;
    transform: none !important;
}

#facebook-login-btn:disabled:hover {
    background-color: #6c757d !important;
    border-color: #6c757d !important;
    transform: none !important;
}

#facebook-login-status {
    transition: all 0.3s ease;
    font-weight: 500;
}

#facebook-login-status.show {
    display: block !important;
}

/* Spinner animation for loading states */
.fa-spinner.fa-spin {
    animation: fa-spin 1s infinite linear;
}

@keyframes fa-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}