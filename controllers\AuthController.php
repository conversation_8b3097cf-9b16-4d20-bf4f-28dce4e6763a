<?php
/**
 * Auth Controller
 * 
 * This controller handles user authentication, registration, and password reset.
 */
class AuthController extends Controller {
    private $userModel;
    private $auth;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->userModel = $this->model('UserModel');
        $this->auth = new Auth();
    }
    
    /**
     * Default index method - redirects to login
     */
    public function index() {
        $this->redirect('auth/login');
    }
    
    /**
     * Smart login routing - shows progressive registration for new users, login for returning users
     */
    public function login() {
        // Check if already logged in
        if ($this->auth->isLoggedIn()) {
            $this->redirect('home/dashboard');
            return;
        }

        // Check if this is a forced login (direct access to showLogin)
        if (!isset($_GET['force']) && $_SERVER['REQUEST_METHOD'] != 'POST') {
            // Debug mode - show what's happening
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('Smart Login: Checking login history for redirect decision');
            }

            // Check if user has ever logged in before (has session history)
            if (!$this->hasLoginHistory()) {
                // New user - show progressive registration
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log('Smart Login: Redirecting to progressive registration');
                }
                $this->redirect('progressive_auth/register');
                return;
            } else {
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log('Smart Login: Showing regular login form');
                }
            }
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, [
                'email' => FILTER_SANITIZE_EMAIL,
                'password' => FILTER_UNSAFE_RAW,
                'remember_me' => FILTER_VALIDATE_BOOLEAN
            ]);
            
            // Get form data
            $data = [
                'email' => trim($_POST['email'] ?? ''),
                'password' => trim($_POST['password'] ?? ''),
                'remember_me' => isset($_POST['remember_me']),
                'email_err' => '',
                'password_err' => '',
                'title' => 'Login'
            ];
            
            // Validate email
            if (empty($data['email'])) {
                $data['email_err'] = 'Please enter your email';
            }
            
            // Validate password
            if (empty($data['password'])) {
                $data['password_err'] = 'Please enter your password';
            }
            
            // Check for errors
            if (empty($data['email_err']) && empty($data['password_err'])) {
                // Attempt to log in with remember me if requested
                $user = $this->auth->login($data['email'], $data['password'], $data['remember_me']);
                
                if ($user) {
                    // Create session
                    $_SESSION['user_id'] = $user->id;
                    $_SESSION['user_role'] = $user->role;
                    $_SESSION['user_name'] = $user->name ?? '';
                    $_SESSION['user_email'] = $user->email;
                    $_SESSION['login_time'] = time();

                    // Set session history cookie for future smart routing (expires in 1 year)
                    setcookie('session_history', '1', time() + (365 * 24 * 60 * 60), '/', '', false, true);

                    // Flag for notification permission manager
                    $_SESSION['just_logged_in'] = true;

                    // Clear PWA auth cache, enable push notifications, and redirect to dashboard
                    echo '<script>
                        // Flag for notification permission manager
                        sessionStorage.setItem("just_logged_in", "true");
                        
                        if (typeof clearAuthCache === "function") {
                            clearAuthCache();
                        }
                        if (typeof enablePushNotifications === "function") {
                            enablePushNotifications();
                        }
                        window.location.href = "' . BASE_URL . '/home/<USER>";
                    </script>';
                    exit;
                } else {
                    $data['password_err'] = 'Invalid email or password';
                    $this->view('auth/login', $data);
                }
            } else {
                // Load view with errors
                $this->view('auth/login', $data);
            }
        } else {
            // Init data
            $data = [
                'email' => '',
                'password' => '',
                'remember_me' => false,
                'email_err' => '',
                'password_err' => '',
                'title' => 'Login'
            ];

            // Load view
            $this->view('auth/login', $data);
        }
    }

    /**
     * Check if user has login history (has logged in before)
     */
    private function hasLoginHistory() {
        // Debug mode - log what cookies we're checking
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log('Smart Login Debug - Checking cookies: ' . print_r($_COOKIE, true));
        }

        // Check for various indicators that user has logged in before

        // 1. Check for remember me cookie
        if (isset($_COOKIE['remember_token'])) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('Smart Login: Found remember_token - returning user');
            }
            return true;
        }

        // 2. Check for session history cookie (set on successful login)
        if (isset($_COOKIE['session_history'])) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('Smart Login: Found session_history - returning user');
            }
            return true;
        }

        // 3. Check for any site-specific cookies that indicate previous login
        if (isset($_COOKIE['user_preferences']) ||
            isset($_COOKIE['last_login']) ||
            isset($_COOKIE['returning_user'])) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('Smart Login: Found other login cookies - returning user');
            }
            return true;
        }

        // 4. For now, only rely on specific login history cookies
        // Don't use PHP session cookie as it's created on every visit

        // No login history found - new user
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log('Smart Login: No login history found - new user, redirecting to progressive registration');
        }
        return false;
    }

    /**
     * Force show login form (for direct access with ?force=1)
     */
    public function showLogin() {
        // Check if already logged in
        if ($this->auth->isLoggedIn()) {
            $this->redirect('home/dashboard');
            return;
        }

        // Force show login regardless of history
        $_GET['force'] = '1';
        $this->login();
    }

    /**
     * Register page - Redirect to Progressive Registration
     */
    public function register() {
        // Check if already logged in
        if ($this->auth->isLoggedIn()) {
            $this->redirect('home/dashboard');
            return;
        }

        // Redirect to progressive registration
        $this->redirect('progressive_auth/register');
    }

    /**
     * Legacy register method (kept for backward compatibility)
     */
    public function registerLegacy() {
        // Check if already logged in
        if ($this->auth->isLoggedIn()) {
            $this->redirect('home/dashboard');
            return;
        }

        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, [
                'name' => FILTER_SANITIZE_FULL_SPECIAL_CHARS,
                'email' => FILTER_SANITIZE_EMAIL,
                'password' => FILTER_UNSAFE_RAW,
                'confirm_password' => FILTER_UNSAFE_RAW,
                'phone' => FILTER_SANITIZE_FULL_SPECIAL_CHARS,
                'address' => FILTER_SANITIZE_FULL_SPECIAL_CHARS,
                'city' => FILTER_SANITIZE_FULL_SPECIAL_CHARS,
                'state' => FILTER_SANITIZE_FULL_SPECIAL_CHARS,
                'zip' => FILTER_SANITIZE_FULL_SPECIAL_CHARS
            ]);
            
            // Get form data
            $data = [
                'name' => trim($_POST['name'] ?? ''),
                'email' => trim($_POST['email'] ?? ''),
                'password' => trim($_POST['password'] ?? ''),
                'confirm_password' => trim($_POST['confirm_password'] ?? ''),
                'phone' => trim($_POST['phone'] ?? ''),
                'address' => trim($_POST['address'] ?? ''),
                'city' => trim($_POST['city'] ?? ''),
                'state' => trim($_POST['state'] ?? ''),
                'zip' => trim($_POST['zip'] ?? ''),
                'name_err' => '',
                'email_err' => '',
                'password_err' => '',
                'confirm_password_err' => '',
                'title' => 'Register'
            ];
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter your name';
            }
            
            // Validate email
            if (empty($data['email'])) {
                $data['email_err'] = 'Please enter your email';
            } elseif ($this->userModel->emailExists($data['email'])) {
                $data['email_err'] = 'Email is already registered';
            }
            
            // Validate password
            if (empty($data['password'])) {
                $data['password_err'] = 'Please enter a password';
            } elseif (strlen($data['password']) < 6) {
                $data['password_err'] = 'Password must be at least 6 characters';
            }
            
            // Validate confirm password
            if (empty($data['confirm_password'])) {
                $data['confirm_password_err'] = 'Please confirm password';
            } elseif ($data['password'] != $data['confirm_password']) {
                $data['confirm_password_err'] = 'Passwords do not match';
            }
            
            // Check for errors
            if (empty($data['name_err']) && empty($data['email_err']) && 
                empty($data['password_err']) && empty($data['confirm_password_err'])) {
                
                // Register user
                $userId = $this->auth->register(
                    $data['name'],
                    $data['email'],
                    $data['password'],
                    'user', // Default role
                    $data['phone'],
                    $data['address'],
                    $data['city'],
                    $data['state'],
                    $data['zip']
                );
                
                if ($userId) {
                    // Create session
                    $_SESSION['user_id'] = $userId;
                    $_SESSION['user_role'] = 'user';
                    
                    // Set flash message
                    // This would require a flash message system
                    
                    // Redirect to dashboard
                    $this->redirect('home/dashboard');
                } else {
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // Load view with errors
                $this->view('auth/register', $data);
            }
        } else {
            // Init data
            $data = [
                'name' => '',
                'email' => '',
                'password' => '',
                'confirm_password' => '',
                'phone' => '',
                'address' => '',
                'city' => '',
                'state' => '',
                'zip' => '',
                'name_err' => '',
                'email_err' => '',
                'password_err' => '',
                'confirm_password_err' => '',
                'title' => 'Register'
            ];
            
            // Load view
            $this->view('auth/register', $data);
        }
    }
    
    /**
     * Facebook login
     * 
     * Initiates the Facebook login process by redirecting to Facebook's OAuth dialog
     */
    public function facebook() {
        // Check if Facebook App ID is configured
        if (empty(FB_APP_ID)) {
            $this->redirect('home/error/Facebook%20login%20not%20configured');
            return;
        }
        
        // Generate and store a CSRF state token for security
        $state = bin2hex(random_bytes(16));
        $_SESSION['fb_state'] = $state;
        
        // Load Facebook service
        require_once APPROOT . '/libraries/facebook/FacebookService.php';
        $fbService = new FacebookService();
        
        // Get login URL
        $fbLoginUrl = $fbService->getLoginUrl($state);
        
        // Redirect to Facebook login dialog
        header('Location: ' . $fbLoginUrl);
        exit;
    }
    
    /**
     * Facebook callback
     * 
     * Handles the callback from Facebook after user authorization
     */
    public function facebookCallback() {
        // Check if user is already logged in (prevent duplicate processing)
        if (isset($_SESSION['user_id']) && !empty($_SESSION['user_id'])) {
            error_log('Facebook callback: User already logged in, redirecting to dashboard');
            $this->redirect('home/dashboard');
            return;
        }

        // Check if Facebook App ID and Secret are configured
        if (empty(FB_APP_ID) || empty(FB_APP_SECRET)) {
            $this->redirect('home/error/Facebook%20login%20not%20configured');
            return;
        }

        // Verify state parameter to prevent CSRF attacks
        if (!isset($_GET['state']) || !isset($_SESSION['fb_state']) || $_GET['state'] !== $_SESSION['fb_state']) {
            error_log('Facebook callback: Invalid state parameter - GET state: ' . ($_GET['state'] ?? 'not set') . ', Session state: ' . ($_SESSION['fb_state'] ?? 'not set'));
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        // Clear the state from session
        unset($_SESSION['fb_state']);
        
        // Check for error response from Facebook
        if (isset($_GET['error'])) {
            error_log('Facebook login error: ' . $_GET['error'] . ' - ' . ($_GET['error_description'] ?? 'No description'));
            $this->redirect('auth/login');
            return;
        }
        
        // Check for authorization code
        if (!isset($_GET['code'])) {
            error_log('Facebook callback: No authorization code received');
            $this->redirect('home/error/Authentication%20failed');
            return;
        }
        
        $code = $_GET['code'];
        
        try {
            // Load Facebook service
            require_once APPROOT . '/libraries/facebook/FacebookService.php';
            $fbService = new FacebookService();
            
            // Exchange authorization code for access token
            $accessToken = $fbService->getAccessToken($code);
            
            if (!$accessToken) {
                throw new Exception('Failed to get access token');
            }
            
            // Get user data from Facebook
            $fbUserData = $fbService->getUserData($accessToken);
            
            if (!$fbUserData) {
                throw new Exception('Failed to get user data from Facebook');
            }
            
            // Register or login with Facebook
            $userId = $this->auth->facebookAuth($fbUserData);
            
            if ($userId) {
                // Get user role
                $user = $this->userModel->getUserById($userId);
                
                // Create session
                $_SESSION['user_id'] = $userId;
                $_SESSION['user_role'] = $user->role;
                $_SESSION['user_name'] = $user->name;
                $_SESSION['user_email'] = $user->email ?? '';
                $_SESSION['login_time'] = time();
                $_SESSION['facebook_login'] = true;
                
                // Log Facebook session creation for debugging
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    $auth = new Auth();
                    error_log('Facebook login successful - Session created for user ID: ' . $userId);
                    error_log('Facebook session login time set to: ' . $_SESSION['login_time']);
                    error_log('Facebook session lifetime: ' . $auth->getSessionLifetime() . ' seconds');
                }

                // Clear PWA auth cache, enable push notifications, and redirect to dashboard
                echo '<script>
                    if (typeof clearAuthCache === "function") {
                        clearAuthCache();
                    }
                    if (typeof enablePushNotifications === "function") {
                        enablePushNotifications();
                    }
                    window.location.href = "' . BASE_URL . '/home/<USER>";
                </script>';
                exit;
            } else {
                throw new Exception('Failed to authenticate user with Facebook data');
            }
            
        } catch (Exception $e) {
            error_log('Facebook authentication error: ' . $e->getMessage());
            $this->redirect('home/error/Facebook%20login%20failed:%20' . urlencode($e->getMessage()));
        }
    }
    
    /**
     * Logout
     */
    public function logout() {
        // Clear remember me cookie if exists
        if (isset($_COOKIE['remember_token'])) {
            setcookie('remember_token', '', time() - 3600, '/');
            
            // Remove token from database
            // This would require a remember_tokens table
        }
        
        // Logout
        $this->auth->logout();

        // Clear PWA auth cache and redirect to login page
        echo '<script>
            if (typeof clearAuthCache === "function") {
                clearAuthCache();
            }
            window.location.href = "' . BASE_URL . '/auth/login";
        </script>';
        exit;
    }
    
    /**
     * Forgot password page
     */
    public function forgotPassword() {
        // Check if already logged in
        if ($this->auth->isLoggedIn()) {
            $this->redirect('home/dashboard');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'email' => trim($_POST['email']),
                'email_err' => '',
                'title' => 'Forgot Password'
            ];
            
            // Validate email
            if (empty($data['email'])) {
                $data['email_err'] = 'Please enter your email';
            } elseif (!$this->userModel->getUserByEmail($data['email'])) {
                $data['email_err'] = 'No account found with that email';
            }
            
            // Check for errors
            if (empty($data['email_err'])) {
                // Generate reset token
                $token = $this->auth->generatePasswordResetToken($data['email']);
                
                if ($token) {
                    // Send reset email
                    // This would require an email system
                    
                    // For now, just show a success message
                    $data['success'] = true;
                    $this->view('auth/forgot_password', $data);
                } else {
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // Load view with errors
                $this->view('auth/forgot_password', $data);
            }
        } else {
            // Init data
            $data = [
                'email' => '',
                'email_err' => '',
                'title' => 'Forgot Password'
            ];
            
            // Load view
            $this->view('auth/forgot_password', $data);
        }
    }
    
    /**
     * Reset password page
     * 
     * @param string $token Reset token
     */
    public function resetPassword($token = null) {
        // Check if already logged in
        if ($this->auth->isLoggedIn()) {
            $this->redirect('home/dashboard');
            return;
        }
        
        // Check if token is valid
        if (!$token) {
            $this->redirect('home/error/Invalid%20token');
            return;
        }
        
        $userId = $this->auth->verifyPasswordResetToken($token);
        
        if (!$userId) {
            $this->redirect('home/error/Invalid%20or%20expired%20token');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'token' => $token,
                'password' => trim($_POST['password']),
                'confirm_password' => trim($_POST['confirm_password']),
                'password_err' => '',
                'confirm_password_err' => '',
                'title' => 'Reset Password'
            ];
            
            // Validate password
            if (empty($data['password'])) {
                $data['password_err'] = 'Please enter a password';
            } elseif (strlen($data['password']) < 6) {
                $data['password_err'] = 'Password must be at least 6 characters';
            }
            
            // Validate confirm password
            if (empty($data['confirm_password'])) {
                $data['confirm_password_err'] = 'Please confirm password';
            } elseif ($data['password'] != $data['confirm_password']) {
                $data['confirm_password_err'] = 'Passwords do not match';
            }
            
            // Check for errors
            if (empty($data['password_err']) && empty($data['confirm_password_err'])) {
                // Reset password
                if ($this->auth->resetPassword($userId, $data['password'])) {
                    // Set success message
                    $data['success'] = true;
                    $this->view('auth/reset_password', $data);
                } else {
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // Load view with errors
                $this->view('auth/reset_password', $data);
            }
        } else {
            // Init data
            $data = [
                'token' => $token,
                'password' => '',
                'confirm_password' => '',
                'password_err' => '',
                'confirm_password_err' => '',
                'title' => 'Reset Password'
            ];
            
            // Load view
            $this->view('auth/reset_password', $data);
        }
    }
}