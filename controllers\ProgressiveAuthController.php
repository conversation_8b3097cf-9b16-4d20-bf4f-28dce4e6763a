<?php
/**
 * Progressive Authentication Controller
 * Handles streamlined registration process
 */

class ProgressiveAuthController extends Controller {
    
    private $userModel;
    private $auth;
    
    public function __construct() {
        $this->userModel = $this->model('UserModel');
        $this->auth = new Auth();
    }
    
    /**
     * Show progressive registration form
     */
    public function register() {
        // Redirect if already logged in
        if ($this->auth->isLoggedIn()) {
            $this->redirect('user/dashboard');
            return;
        }
        
        $data = [
            'title' => 'Join the Community',
            'step' => 1,
            'email' => $_SESSION['temp_email'] ?? '',
            'conversion_source' => $_GET['source'] ?? 'direct'
        ];
        
        $this->view('auth/progressive-register', $data);
    }
    
    /**
     * Handle progressive registration steps via AJAX
     */
    public function processStep() {
        // Verify CSRF token
        if (!$this->verifyCsrfToken()) {
            $this->jsonResponse(['success' => false, 'error' => 'Invalid request']);
            return;
        }
        
        $step = $_POST['step'] ?? 1;
        
        switch ($step) {
            case 1:
                $this->processStepOne();
                break;
            case 2:
                $this->processStepTwo();
                break;
            case 3:
                $this->processStepThree();
                break;
            default:
                $this->jsonResponse(['success' => false, 'error' => 'Invalid step']);
        }
    }
    
    /**
     * Process step 1: Email validation
     */
    private function processStepOne() {
        $email = filter_var($_POST['email'], FILTER_VALIDATE_EMAIL);
        
        if (!$email) {
            $this->jsonResponse(['success' => false, 'error' => 'Please enter a valid email address']);
            return;
        }
        
        // Check if email already exists
        if ($this->userModel->emailExists($email)) {
            $this->jsonResponse([
                'success' => false, 
                'error' => 'This email is already registered. <a href="' . BASE_URL . '/auth/login">Sign in instead?</a>',
                'existing_user' => true
            ]);
            return;
        }
        
        // Store email temporarily
        $_SESSION['temp_email'] = $email;
        $_SESSION['registration_step'] = 1;
        
        $this->jsonResponse([
            'success' => true,
            'next_step' => 2,
            'message' => 'Great! Let\'s create your account.'
        ]);
    }
    
    /**
     * Process step 2: Basic account creation
     */
    private function processStepTwo() {
        // Validate required fields
        $email = $_SESSION['temp_email'] ?? '';
        $name = trim($_POST['name'] ?? '');
        $password = $_POST['password'] ?? '';
        
        if (empty($email) || empty($name) || empty($password)) {
            $this->jsonResponse(['success' => false, 'error' => 'Please fill in all required fields']);
            return;
        }
        
        if (strlen($password) < 6) {
            $this->jsonResponse(['success' => false, 'error' => 'Password must be at least 6 characters long']);
            return;
        }
        
        // Create user account
        $userData = [
            'name' => $name,
            'email' => $email,
            'password' => password_hash($password, PASSWORD_DEFAULT),
            'role' => 'user',
            'status' => 'active',
            'registration_step' => 2,
            'registration_source' => $_POST['source'] ?? 'progressive',
            'created_at' => gmdate('Y-m-d H:i:s')
        ];
        
        $userId = $this->userModel->createUser($userData);
        
        if ($userId) {
            // Auto-login the user
            $_SESSION['user_id'] = $userId;
            $_SESSION['user_name'] = $name;
            $_SESSION['user_email'] = $email;
            $_SESSION['user_role'] = 'user';
            $_SESSION['role_just_upgraded'] = true; // Trigger celebration
            
            // Clear temporary data
            unset($_SESSION['temp_email']);
            unset($_SESSION['registration_step']);
            
            $this->jsonResponse([
                'success' => true,
                'next_step' => 3,
                'message' => 'Account created successfully!',
                'user_id' => $userId,
                'redirect' => BASE_URL . '/user/welcome'
            ]);
        } else {
            $this->jsonResponse(['success' => false, 'error' => 'Failed to create account. Please try again.']);
        }
    }
    
    /**
     * Process step 3: Optional profile completion
     */
    private function processStepThree() {
        if (!$this->auth->isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'error' => 'Please log in first']);
            return;
        }
        
        $userId = $this->auth->getCurrentUserId();
        $location = trim($_POST['location'] ?? '');
        $interests = $_POST['interests'] ?? [];
        
        // Update user profile
        $profileData = [
            'location' => $location,
            'interests' => is_array($interests) ? implode(',', $interests) : '',
            'registration_step' => 3,
            'profile_completed' => 1
        ];
        
        if ($this->userModel->updateUser($userId, $profileData)) {
            $this->jsonResponse([
                'success' => true,
                'message' => 'Profile completed!',
                'redirect' => BASE_URL . '/user/dashboard'
            ]);
        } else {
            // Even if profile update fails, registration was successful
            $this->jsonResponse([
                'success' => true,
                'message' => 'Welcome to the community!',
                'redirect' => BASE_URL . '/user/dashboard'
            ]);
        }
    }
    
    /**
     * Handle Facebook registration
     */
    public function facebook() {
        // This would integrate with Facebook SDK
        // For now, redirect to existing Facebook auth
        $this->redirect('auth/facebook');
    }
    
    /**
     * Welcome page for new users
     */
    public function welcome() {
        if (!$this->auth->isLoggedIn()) {
            $this->redirect('auth/login');
            return;
        }
        
        $userId = $this->auth->getCurrentUserId();
        $user = $this->userModel->getUserById($userId);
        
        $data = [
            'title' => 'Welcome to the Community!',
            'user' => $user,
            'show_onboarding' => true
        ];
        
        $this->view('auth/welcome', $data);
    }
    
    /**
     * Check email availability (AJAX endpoint)
     */
    public function checkEmail() {
        $email = filter_var($_POST['email'] ?? '', FILTER_VALIDATE_EMAIL);
        
        if (!$email) {
            $this->jsonResponse(['available' => false, 'error' => 'Invalid email format']);
            return;
        }
        
        $exists = $this->userModel->emailExists($email);
        
        $this->jsonResponse([
            'available' => !$exists,
            'message' => $exists ? 'Email already registered' : 'Email available'
        ]);
    }
    
    /**
     * Get registration statistics for social proof
     */
    public function getStats() {
        // Get user and show counts for social proof
        $userCount = $this->userModel->getTotalUserCount();
        $showCount = $this->model('ShowModel')->getActiveShowCount();
        
        $this->jsonResponse([
            'users' => $userCount,
            'shows' => $showCount,
            'events' => $this->model('CalendarModel')->getActiveEventCount()
        ]);
    }
    
    /**
     * Handle registration abandonment recovery
     */
    public function recover() {
        $email = $_GET['email'] ?? '';
        $token = $_GET['token'] ?? '';
        
        // Validate recovery token (implement token validation logic)
        if ($this->validateRecoveryToken($email, $token)) {
            $_SESSION['temp_email'] = $email;
            $_SESSION['registration_step'] = 1;
            
            $data = [
                'title' => 'Complete Your Registration',
                'email' => $email,
                'recovery_mode' => true
            ];
            
            $this->view('auth/progressive-register', $data);
        } else {
            $this->redirect('auth/register');
        }
    }
    
    /**
     * Validate recovery token
     */
    private function validateRecoveryToken($email, $token) {
        // Implement token validation logic
        // This would check against a database table of recovery tokens
        return false; // Placeholder
    }
    
}
