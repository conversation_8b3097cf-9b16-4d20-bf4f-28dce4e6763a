# Changelog

All notable changes to the Events and Shows Management System will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [3.69.0] - 2025-01-28

### Changed
- **Navigation Menu Consolidation** - Merged EVENTS and SHOWS menus into single CALENDAR menu
- Replaced separate "Events" and "Shows" dropdown menus with unified "Calendar" menu
- Updated desktop navigation with emoji icons: 📅 Calendar, 🏆 Shows, ➕ Add Event, 🎯 Create Show
- Consolidated mobile drawer navigation from two buttons to single CALENDAR button
- Improved mobile-first responsive design with cleaner menu structure
- Reduced visitor confusion by eliminating duplicate calendar links
- Enhanced user experience with clear distinction between simple events and full car shows
- **Calendar Page Title Update** - Changed page title from "Monthly Event Chart" to "Monthly Calendar"
- **Header Cleanup** - Completely removed redundant role badge and switch view from header
- **Enhanced Breadcrumb Bar** - Added racing header styling with carbon fiber pattern and chrome effects
- **Role Indicator Enhancement** - Added role icons and restored hover tooltips with role capabilities descriptions
- **Racing Switch Dropdown** - Styled admin switch view dropdown to match racing header theme
- **Home Page Breadcrumb** - Display breadcrumb bar on home page for logged-in users to show role context

## [3.67.10] - 2024-12-19

### Changed
- Removed green shadow from "Get Directions" button in calendar map popups
- Added box-shadow: none to all info window buttons for cleaner appearance
- Enhanced button styling consistency across hover and focus states

## [3.67.9] - 2024-12-19

### Fixed
- Fixed JavaScript ReferenceError for getDirectionsToEvent function
- Moved getDirectionsToEvent function to global scope to resolve onclick handler issues
- Removed duplicate function definition that was causing conflicts

## [3.67.8] - 2024-12-19

### Added
- Get Directions button in calendar map event popups
- User address integration for calendar map directions
- Enhanced popup styling for better button layout

### Changed
- Calendar map popups now include directions functionality similar to show view
- Improved info window styling with better button spacing and hover effects
