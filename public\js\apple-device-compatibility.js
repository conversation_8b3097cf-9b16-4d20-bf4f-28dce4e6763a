/**
 * Apple Device Compatibility JavaScript
 * Enhanced support for iOS Safari, iPhone, iPad, and other Apple devices
 * Version: 1.0.0
 */

(function() {
    'use strict';
    
    // Detect Apple devices
    const isAppleDevice = /iPad|iPhone|iPod/.test(navigator.userAgent) ||
                         (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
    const isIOSSafari = /iPad|iPhone|iPod/.test(navigator.userAgent) && /Safari/.test(navigator.userAgent);
    const isStandalone = window.navigator.standalone === true;
    const isIPad = /iPad/.test(navigator.userAgent) ||
                   (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
    
    console.log('🍎 Apple Device Compatibility: Initializing...');
    console.log('🍎 Is Apple Device:', isAppleDevice);
    console.log('🍎 Is iOS Safari:', isIOSSafari);
    console.log('🍎 Is PWA Standalone:', isStandalone);
    console.log('🍎 Is iPad:', isIPad);
    console.log('🍎 Screen dimensions:', window.innerWidth + 'x' + window.innerHeight);
    console.log('🍎 Device pixel ratio:', window.devicePixelRatio);
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initAppleCompatibility);
    } else {
        initAppleCompatibility();
    }
    
    function initAppleCompatibility() {
        if (!isAppleDevice) {
            console.log('🍎 Not an Apple device, skipping Apple-specific enhancements');
            return;
        }
        
        console.log('🍎 Initializing Apple device enhancements...');
        
        // Add Apple device class to body
        document.body.classList.add('apple-device');
        if (isIOSSafari) document.body.classList.add('ios-safari');
        if (isStandalone) document.body.classList.add('ios-standalone');
        if (isIPad) document.body.classList.add('ipad-device');
        
        // Initialize all Apple-specific features
        fixIOSViewportHeight();
        enhanceIOSTouchEvents();
        fixIOSFormZoom();
        setupIOSScrollFixes();
        enhanceIOSModalHandling();
        setupIOSOrientationHandling();
        setupIOSKeyboardHandling();

        // iPad-specific fixes
        if (isIPad) {
            setupIPadNavigationFix();
        }
        
        console.log('🍎 Apple device enhancements initialized successfully');
    }
    
    /**
     * Fix iOS Safari viewport height issues
     */
    function fixIOSViewportHeight() {
        if (!isIOSSafari) return;
        
        function setViewportHeight() {
            const vh = window.innerHeight * 0.01;
            document.documentElement.style.setProperty('--vh', `${vh}px`);
        }
        
        setViewportHeight();
        window.addEventListener('resize', setViewportHeight);
        window.addEventListener('orientationchange', () => {
            setTimeout(setViewportHeight, 100);
        });
        
        console.log('🍎 iOS viewport height fix applied');
    }
    
    /**
     * Enhance touch events for iOS devices
     */
    function enhanceIOSTouchEvents() {
        // Add haptic feedback simulation for buttons
        const buttons = document.querySelectorAll('.btn, button, .racing-menu-btn, .nav-link');
        
        buttons.forEach(button => {
            button.addEventListener('touchstart', function(e) {
                this.classList.add('ios-touch-active');
                
                // Simulate haptic feedback with visual feedback
                if (this.style.transform !== 'scale(0.98)') {
                    this.style.transform = 'scale(0.98)';
                    this.style.transition = 'transform 0.1s ease';
                }
            }, { passive: true });
            
            button.addEventListener('touchend', function(e) {
                this.classList.remove('ios-touch-active');
                
                setTimeout(() => {
                    this.style.transform = '';
                }, 100);
            }, { passive: true });
            
            button.addEventListener('touchcancel', function(e) {
                this.classList.remove('ios-touch-active');
                this.style.transform = '';
            }, { passive: true });
        });
        
        console.log('🍎 iOS touch events enhanced for', buttons.length, 'elements');
    }
    
    /**
     * Fix iOS Safari form input zoom
     */
    function fixIOSFormZoom() {
        if (!isIOSSafari) return;
        
        const inputs = document.querySelectorAll('input[type="text"], input[type="email"], input[type="password"], input[type="tel"], input[type="url"], input[type="search"], textarea, select');
        
        inputs.forEach(input => {
            // Ensure font-size is at least 16px to prevent zoom
            const computedStyle = window.getComputedStyle(input);
            const fontSize = parseFloat(computedStyle.fontSize);
            
            if (fontSize < 16) {
                input.style.fontSize = '16px';
            }
        });
        
        console.log('🍎 iOS form zoom prevention applied to', inputs.length, 'inputs');
    }
    
    /**
     * Setup iOS scroll fixes
     */
    function setupIOSScrollFixes() {
        if (!isIOSSafari) return;
        
        // Fix momentum scrolling
        document.body.style.webkitOverflowScrolling = 'touch';
        
        // Fix scroll position after orientation change
        window.addEventListener('orientationchange', function() {
            setTimeout(() => {
                window.scrollTo(0, window.pageYOffset);
            }, 100);
        });
        
        console.log('🍎 iOS scroll fixes applied');
    }
    
    /**
     * Enhance modal handling for iOS
     */
    function enhanceIOSModalHandling() {
        if (!isIOSSafari) return;
        
        // Monitor for modal creation
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1 && node.classList && node.classList.contains('modal')) {
                        enhanceModalForIOS(node);
                    }
                });
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        // Enhance existing modals
        document.querySelectorAll('.modal').forEach(enhanceModalForIOS);
        
        function enhanceModalForIOS(modal) {
            // Fix iOS modal backdrop issues
            modal.style.webkitTransform = 'translateZ(0)';
            modal.style.transform = 'translateZ(0)';
            
            const modalDialog = modal.querySelector('.modal-dialog');
            if (modalDialog) {
                modalDialog.style.webkitTransform = 'translateZ(0)';
                modalDialog.style.transform = 'translateZ(0)';
            }
            
            const modalContent = modal.querySelector('.modal-content');
            if (modalContent) {
                modalContent.style.webkitTransform = 'translateZ(0)';
                modalContent.style.transform = 'translateZ(0)';
                modalContent.style.webkitBackfaceVisibility = 'hidden';
                modalContent.style.backfaceVisibility = 'hidden';
            }
            
            // Fix iOS modal scrolling
            modal.style.webkitOverflowScrolling = 'touch';
        }
        
        console.log('🍎 iOS modal enhancements applied');
    }
    
    /**
     * Setup iOS orientation change handling
     */
    function setupIOSOrientationHandling() {
        if (!isAppleDevice) return;
        
        let orientationTimeout;
        
        window.addEventListener('orientationchange', function() {
            // Clear any existing timeout
            if (orientationTimeout) {
                clearTimeout(orientationTimeout);
            }
            
            // Add orientation change class
            document.body.classList.add('orientation-changing');
            
            // Remove class and fix layout after orientation change completes
            orientationTimeout = setTimeout(() => {
                document.body.classList.remove('orientation-changing');
                
                // Trigger resize event to fix any layout issues
                window.dispatchEvent(new Event('resize'));
                
                // Fix any stuck modals
                const openModals = document.querySelectorAll('.modal.show');
                openModals.forEach(modal => {
                    modal.style.display = 'none';
                    setTimeout(() => {
                        modal.style.display = '';
                    }, 10);
                });
                
            }, 500);
        });
        
        console.log('🍎 iOS orientation change handling setup');
    }
    
    /**
     * Setup iOS keyboard handling
     */
    function setupIOSKeyboardHandling() {
        if (!isIOSSafari) return;
        
        let initialViewportHeight = window.innerHeight;
        
        // Detect virtual keyboard
        window.addEventListener('resize', function() {
            const currentHeight = window.innerHeight;
            const heightDifference = initialViewportHeight - currentHeight;
            
            if (heightDifference > 150) {
                // Keyboard is likely open
                document.body.classList.add('ios-keyboard-open');
            } else {
                // Keyboard is likely closed
                document.body.classList.remove('ios-keyboard-open');
            }
        });
        
        // Fix scroll position when keyboard closes
        document.addEventListener('focusout', function(e) {
            if (e.target.matches('input, textarea, select')) {
                setTimeout(() => {
                    window.scrollTo(0, 0);
                }, 100);
            }
        });
        
        console.log('🍎 iOS keyboard handling setup');
    }
    
    /**
     * Global function to force refresh iOS layout
     */
    window.refreshIOSLayout = function() {
        if (!isAppleDevice) return;
        
        console.log('🍎 Forcing iOS layout refresh...');
        
        // Trigger reflow
        document.body.style.display = 'none';
        document.body.offsetHeight; // Force reflow
        document.body.style.display = '';
        
        // Dispatch resize event
        window.dispatchEvent(new Event('resize'));
        
        console.log('🍎 iOS layout refresh completed');
    };
    
    /**
     * Setup iPad navigation fix - ensure PWA mobile nav works in portrait
     */
    function setupIPadNavigationFix() {
        if (!isIPad) return;

        console.log('🍎 Setting up iPad navigation fix...');

        function checkAndFixNavigation() {
            const isPortrait = window.innerHeight > window.innerWidth;
            const screenWidth = window.innerWidth;

            console.log('🍎 iPad navigation check:', {
                orientation: isPortrait ? 'Portrait' : 'Landscape',
                screenWidth: screenWidth,
                shouldUseMobile: isPortrait || screenWidth < 992
            });

            if (isPortrait || screenWidth < 992) {
                // Force mobile navigation for iPad portrait
                forceMobileNavigation();
            } else {
                // Allow desktop navigation for iPad landscape
                allowDesktopNavigation();
            }
        }

        // Check on load
        checkAndFixNavigation();

        // Check on orientation change
        window.addEventListener('orientationchange', () => {
            setTimeout(checkAndFixNavigation, 200);
        });

        // Check on resize
        window.addEventListener('resize', () => {
            setTimeout(checkAndFixNavigation, 100);
        });
    }

    /**
     * Force mobile navigation for iPad portrait
     */
    function forceMobileNavigation() {
        console.log('🍎 Forcing mobile navigation for iPad...');

        // Hide ALL desktop navigation elements
        const desktopNav = document.querySelector('.racing-header .navbar-collapse');
        if (desktopNav) {
            desktopNav.style.display = 'none';
            console.log('🍎 Hidden navbar-collapse');
        }

        const desktopNavFlex = document.querySelector('.racing-header .d-none.d-lg-flex');
        if (desktopNavFlex) {
            desktopNavFlex.style.display = 'none';
            console.log('🍎 Hidden d-lg-flex navigation');
        }

        // Hide all navbar-nav elements in header
        const navbarNavs = document.querySelectorAll('.racing-header .navbar-nav');
        navbarNavs.forEach(nav => {
            nav.style.display = 'none';
            console.log('🍎 Hidden navbar-nav element');
        });

        // Show mobile menu button
        const mobileMenuBtn = document.querySelector('.racing-menu-btn.d-lg-none');
        if (mobileMenuBtn) {
            mobileMenuBtn.style.display = 'block !important';
            mobileMenuBtn.classList.remove('d-none');
        }

        // Ensure PWA bottom navigation is visible - create if it doesn't exist
        let mobileNavBottom = document.querySelector('.mobile-nav-bottom');
        if (!mobileNavBottom) {
            console.log('🍎 Creating mobile navigation for iPad...');
            createMobileNavigationForIPad();
            mobileNavBottom = document.querySelector('.mobile-nav-bottom');
        }

        if (mobileNavBottom) {
            mobileNavBottom.style.display = 'block';
            mobileNavBottom.classList.add('show');
            console.log('🍎 Mobile navigation is now visible');
        }

        // Add bottom padding to body for mobile nav
        document.body.style.paddingBottom = '80px';

        // Add class to body for styling
        document.body.classList.add('ipad-mobile-mode');
        document.body.classList.remove('ipad-desktop-mode');

        console.log('🍎 Mobile navigation forced for iPad');
    }

    /**
     * Allow desktop navigation for iPad landscape
     */
    function allowDesktopNavigation() {
        console.log('🍎 Allowing desktop navigation for iPad...');

        // Show ALL desktop navigation elements
        const desktopNav = document.querySelector('.racing-header .navbar-collapse');
        if (desktopNav) {
            desktopNav.style.display = '';
            console.log('🍎 Restored navbar-collapse');
        }

        const desktopNavFlex = document.querySelector('.racing-header .d-none.d-lg-flex');
        if (desktopNavFlex) {
            desktopNavFlex.style.display = '';
            console.log('🍎 Restored d-lg-flex navigation');
        }

        // Show all navbar-nav elements in header
        const navbarNavs = document.querySelectorAll('.racing-header .navbar-nav');
        navbarNavs.forEach(nav => {
            nav.style.display = '';
            console.log('🍎 Restored navbar-nav element');
        });

        // Hide mobile menu button
        const mobileMenuBtn = document.querySelector('.racing-menu-btn.d-lg-none');
        if (mobileMenuBtn) {
            mobileMenuBtn.style.display = 'none !important';
            mobileMenuBtn.classList.add('d-none');
        }

        // Hide PWA bottom navigation
        const mobileNavBottom = document.querySelector('.mobile-nav-bottom');
        if (mobileNavBottom) {
            mobileNavBottom.style.display = 'none';
            mobileNavBottom.classList.remove('show');
        }

        // Remove bottom padding from body
        document.body.style.paddingBottom = '';

        // Add class to body for styling
        document.body.classList.add('ipad-desktop-mode');
        document.body.classList.remove('ipad-mobile-mode');

        console.log('🍎 Desktop navigation allowed for iPad');
    }

    /**
     * Create mobile navigation for iPad if it doesn't exist
     */
    function createMobileNavigationForIPad() {
        // Check if it already exists
        if (document.querySelector('.mobile-nav-bottom')) {
            return;
        }

        console.log('🍎 Creating mobile navigation for iPad...');

        // Create the mobile navigation element
        const mobileNav = document.createElement('div');
        mobileNav.className = 'mobile-nav-bottom show';

        // Get base URL from window or use current origin
        const baseUrl = window.BASE_URL || window.location.origin;

        mobileNav.innerHTML = `
            <div class="mobile-nav-items">
                <a href="${baseUrl}/" class="mobile-nav-item">
                    <i class="fas fa-home"></i>
                    <span>Home</span>
                </a>
                <a href="${baseUrl}/calendar" class="mobile-nav-item">
                    <i class="fas fa-calendar"></i>
                    <span>Calendar</span>
                </a>
                <a href="${baseUrl}/home/<USER>" class="mobile-nav-item center-dashboard">
                    <div class="speedometer-icon">
                        <img src="${baseUrl}/public/images/dashboard.png" alt="Dashboard" class="dashboard-image">
                    </div>
                    <span>Dashboard</span>
                </a>
                <a href="${baseUrl}/notification_center" class="mobile-nav-item">
                    <i class="fas fa-bell"></i>
                    <span>Messages</span>
                </a>
                <a href="#" class="mobile-nav-item" onclick="forceAppRefresh()" style="color: #1338BE;">
                    <i class="fas fa-sync-alt"></i>
                    <span>Refresh</span>
                </a>
            </div>
        `;

        document.body.appendChild(mobileNav);
        console.log('🍎 Mobile navigation created and added to page');
    }

    // Removed complex layout functions - keeping it simple

    /**
     * Apply extreme space-saving measures for iPad portrait if needed
     */
    function checkAndApplyExtremePortraitFixes() {
        if (!isIPad || window.innerHeight <= window.innerWidth) return;

        const userDropdown = document.querySelector('.racing-header .nav-item.dropdown:last-child');
        if (!userDropdown) return;

        const rect = userDropdown.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const isOverflowing = rect.right > viewportWidth;

        console.log('🍎 Portrait overflow check:', {
            userMenuRight: rect.right,
            viewportWidth: viewportWidth,
            isOverflowing: isOverflowing,
            overflowBy: isOverflowing ? rect.right - viewportWidth : 0
        });

        if (isOverflowing) {
            console.log('🍎 Applying EXTREME portrait fixes...');

            // Hide text in navigation, keep only icons
            const navLinks = document.querySelectorAll('.racing-header .racing-nav-link');
            navLinks.forEach(link => {
                const icon = link.querySelector('i');
                const text = link.childNodes;

                // Hide text nodes, keep icons
                text.forEach(node => {
                    if (node.nodeType === 3 && node.textContent.trim()) { // Text node
                        node.textContent = '';
                    }
                });

                if (icon) {
                    link.style.padding = '0.3rem 0.1rem';
                    link.style.fontSize = '0.8rem';
                }
            });

            // Make brand even smaller
            const brand = document.querySelector('.racing-brand');
            if (brand) {
                brand.style.fontSize = '0.7rem';
                brand.style.marginRight = '0.1rem';
            }

            // Ultra-compact container
            const container = document.querySelector('.racing-header .container');
            if (container) {
                container.style.paddingLeft = '2px';
                container.style.paddingRight = '2px';
            }

            console.log('🍎 EXTREME portrait fixes applied');
        }
    }

    // Removed complex layout monitoring

    /**
     * Simple iPad layout analysis
     */
    window.analyzeIPadLayout = function() {
        if (!isIPad) {
            console.log('🍎 Not an iPad device');
            return;
        }

        console.log('🍎 iPad detected:', {
            orientation: window.innerHeight > window.innerWidth ? 'Portrait' : 'Landscape',
            screenWidth: window.innerWidth,
            screenHeight: window.innerHeight
        });
    };

    /**
     * Global function to force iPad layout refresh (simplified)
     */
    window.refreshIPadLayout = function() {
        if (!isIPad) {
            console.log('🍎 Not an iPad device');
            return;
        }

        console.log('🍎 iPad layout refresh - minimal fixes only');

        // Just trigger general iOS layout refresh
        if (window.refreshIOSLayout) {
            window.refreshIOSLayout();
        }
    };

    /**
     * Export compatibility info for debugging
     */
    window.appleDeviceInfo = {
        isAppleDevice,
        isIOSSafari,
        isStandalone,
        isIPad,
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        maxTouchPoints: navigator.maxTouchPoints,
        standalone: window.navigator.standalone,
        screenWidth: window.innerWidth,
        screenHeight: window.innerHeight,
        devicePixelRatio: window.devicePixelRatio
    };
    
})();
